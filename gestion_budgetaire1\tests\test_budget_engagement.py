# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError, UserError
from datetime import date


class TestBudgetEngagement(TransactionCase):
    """Tests pour le modèle budget.engagement"""

    def setUp(self):
        super().setUp()
        self.BudgetEngagement = self.env['budget.engagement']
        self.BudgetExercise = self.env['budget.exercise']
        self.BudgetNomenclature = self.env['budget.nomenclature']
        self.BudgetCredit = self.env['budget.credit']
        self.BudgetEngagementType = self.env['budget.engagement.type']
        
        self.company = self.env.ref('base.main_company')
        self.partner = self.env.ref('base.res_partner_1')
        
        # Créer un exercice de test
        self.exercise = self.BudgetExercise.create({
            'name': 'Test Exercise 2024',
            'code': 'TEST2024',
            'date_start': date(2024, 1, 1),
            'date_end': date(2024, 12, 31),
            'company_id': self.company.id,
            'state': 'open',
        })
        
        # Créer une nomenclature de test
        self.nomenclature = self.BudgetNomenclature.create({
            'name': 'Test Nomenclature',
            'code': 'TEST001',
            'budget_type': 'expense',
            'nature': 'article',
            'is_analytical': True,
            'company_id': self.company.id,
        })
        
        # Créer un crédit de test
        self.credit = self.BudgetCredit.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'amount_initial': 100000.0,
            'state': 'validated',
        })
        
        # Créer un type d'engagement de test
        self.engagement_type = self.BudgetEngagementType.create({
            'name': 'Test Engagement Type',
            'code': 'TEST',
            'budget_impact': 'consumption',
            'requires_validation': True,
            'company_id': self.company.id,
        })

    def test_create_engagement(self):
        """Test de création d'un engagement"""
        engagement = self.BudgetEngagement.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'engagement_type_id': self.engagement_type.id,
            'partner_id': self.partner.id,
            'amount': 50000.0,
            'description': 'Test engagement',
        })
        
        self.assertEqual(engagement.state, 'draft')
        self.assertEqual(engagement.amount, 50000.0)
        self.assertEqual(engagement.credit_id, self.credit)

    def test_engagement_amount_validation(self):
        """Test de validation du montant d'engagement"""
        # Test montant négatif
        with self.assertRaises(ValidationError):
            self.BudgetEngagement.create({
                'exercise_id': self.exercise.id,
                'nomenclature_id': self.nomenclature.id,
                'engagement_type_id': self.engagement_type.id,
                'partner_id': self.partner.id,
                'amount': -1000.0,
                'description': 'Invalid engagement',
            })

    def test_engagement_date_validation(self):
        """Test de validation de la date d'engagement"""
        # Test date hors exercice
        with self.assertRaises(ValidationError):
            self.BudgetEngagement.create({
                'exercise_id': self.exercise.id,
                'nomenclature_id': self.nomenclature.id,
                'engagement_type_id': self.engagement_type.id,
                'partner_id': self.partner.id,
                'amount': 10000.0,
                'date': date(2023, 12, 31),  # Hors exercice
                'description': 'Invalid date engagement',
            })

    def test_engagement_workflow(self):
        """Test du workflow d'engagement"""
        engagement = self.BudgetEngagement.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'engagement_type_id': self.engagement_type.id,
            'partner_id': self.partner.id,
            'amount': 30000.0,
            'description': 'Workflow test engagement',
        })
        
        # Test soumission pour validation
        engagement.action_submit_for_validation()
        self.assertEqual(engagement.state, 'waiting_validation')
        
        # Test validation
        engagement.action_validate()
        self.assertEqual(engagement.state, 'validated')
        
        # Vérifier l'impact sur le crédit
        self.credit._compute_amounts()
        self.assertEqual(self.credit.amount_engaged, 30000.0)
        self.assertEqual(self.credit.amount_available, 70000.0)

    def test_engagement_budget_availability(self):
        """Test de vérification de la disponibilité budgétaire"""
        # Créer un engagement qui dépasse le crédit disponible
        with self.assertRaises(UserError):
            engagement = self.BudgetEngagement.create({
                'exercise_id': self.exercise.id,
                'nomenclature_id': self.nomenclature.id,
                'engagement_type_id': self.engagement_type.id,
                'partner_id': self.partner.id,
                'amount': 150000.0,  # Dépasse le crédit de 100000
                'description': 'Excessive engagement',
            })
            engagement.action_submit_for_validation()
            engagement.action_validate()

    def test_engagement_cancellation(self):
        """Test d'annulation d'engagement"""
        engagement = self.BudgetEngagement.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'engagement_type_id': self.engagement_type.id,
            'partner_id': self.partner.id,
            'amount': 20000.0,
            'description': 'Cancellation test',
        })
        
        engagement.action_submit_for_validation()
        engagement.action_validate()
        
        # Vérifier l'impact initial
        self.credit._compute_amounts()
        initial_engaged = self.credit.amount_engaged
        
        # Annuler l'engagement
        engagement.action_cancel()
        self.assertEqual(engagement.state, 'cancelled')
        
        # Vérifier que le crédit est libéré
        self.credit._compute_amounts()
        self.assertEqual(self.credit.amount_engaged, initial_engaged - 20000.0)

    def test_engagement_mandate_creation(self):
        """Test de création de mandat depuis un engagement"""
        engagement = self.BudgetEngagement.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'engagement_type_id': self.engagement_type.id,
            'partner_id': self.partner.id,
            'amount': 40000.0,
            'description': 'Mandate test engagement',
        })
        
        engagement.action_submit_for_validation()
        engagement.action_validate()
        
        # Créer un mandat
        mandate_action = engagement.action_create_mandate()
        self.assertIn('res_model', mandate_action)
        self.assertEqual(mandate_action['res_model'], 'budget.mandate')

    def test_engagement_amounts_computation(self):
        """Test du calcul des montants d'engagement"""
        engagement = self.BudgetEngagement.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'engagement_type_id': self.engagement_type.id,
            'partner_id': self.partner.id,
            'amount': 60000.0,
            'description': 'Amounts test engagement',
        })
        
        engagement.action_submit_for_validation()
        engagement.action_validate()
        
        # Initialement, rien n'est mandaté
        self.assertEqual(engagement.amount_mandated, 0.0)
        self.assertEqual(engagement.amount_remaining, 60000.0)

    def test_engagement_sequence_generation(self):
        """Test de génération automatique de la séquence"""
        engagement = self.BudgetEngagement.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'engagement_type_id': self.engagement_type.id,
            'partner_id': self.partner.id,
            'amount': 25000.0,
            'description': 'Sequence test engagement',
        })
        
        # Le nom doit être généré automatiquement
        self.assertNotEqual(engagement.name, 'Nouveau')
        self.assertTrue(engagement.name.startswith('ENG/'))

    def test_engagement_credit_computation(self):
        """Test du calcul automatique du crédit"""
        engagement = self.BudgetEngagement.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'engagement_type_id': self.engagement_type.id,
            'partner_id': self.partner.id,
            'amount': 35000.0,
            'description': 'Credit computation test',
        })
        
        # Le crédit doit être calculé automatiquement
        self.assertEqual(engagement.credit_id, self.credit)
