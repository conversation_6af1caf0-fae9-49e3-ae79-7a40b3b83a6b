<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Vue liste hiérarchique de la nomenclature -->
        <record id="view_budget_nomenclature_tree" model="ir.ui.view">
            <field name="name">budget.nomenclature.tree</field>
            <field name="model">budget.nomenclature</field>
            <field name="arch" type="xml">
                <tree string="Nomenclature Budgétaire" decoration-bf="not parent_id"
                      decoration-info="budget_type=='expense'" decoration-success="budget_type=='revenue'">
                    <field name="complete_code"/>
                    <field name="name"/>
                    <field name="nature" widget="badge"/>
                    <field name="budget_type" widget="badge"
                           decoration-info="budget_type=='expense'"
                           decoration-success="budget_type=='revenue'"/>
                    <field name="is_analytical" widget="boolean_toggle"/>
                    <field name="level" invisible="1"/>
                    <field name="parent_id" invisible="1"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <!-- Vue formulaire de la nomenclature -->
        <record id="view_budget_nomenclature_form" model="ir.ui.view">
            <field name="name">budget.nomenclature.form</field>
            <field name="model">budget.nomenclature</field>
            <field name="arch" type="xml">
                <form string="Poste Budgétaire">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_credits" type="object"
                                    class="oe_stat_button" icon="fa-money"
                                    invisible="not is_analytical">
                                <div class="o_stat_info">
                                    <span class="o_stat_text">Crédits</span>
                                </div>
                            </button>
                            <button name="action_view_engagements" type="object"
                                    class="oe_stat_button" icon="fa-file-text-o"
                                    invisible="not is_analytical">
                                <div class="o_stat_info">
                                    <span class="o_stat_text">Engagements</span>
                                </div>
                            </button>
                        </div>

                        <widget name="web_ribbon" title="Archivé" bg_color="bg-danger"
                                invisible="active"/>

                        <div class="oe_title">
                            <h1>
                                <field name="complete_code" readonly="1"/>
                                <span> - </span>
                                <field name="name" placeholder="Libellé du poste budgétaire"/>
                            </h1>
                        </div>

                        <group>
                            <group>
                                <field name="code"/>
                                <field name="parent_id" domain="[('company_id', '=', company_id)]"/>
                                <field name="sequence"/>
                                <field name="active"/>
                            </group>
                            <group>
                                <field name="nature"/>
                                <field name="budget_type"/>
                                <field name="is_analytical"/>
                                <field name="level" readonly="1"/>
                                <field name="company_id" invisible="not context.get('show_company', False)"/>
                            </group>
                        </group>

                        <group string="Intégration Comptable">
                            <group>
                                <field name="account_analytic_id"/>
                            </group>
                            <group>
                                <field name="account_ids" widget="many2many_tags"/>
                            </group>
                        </group>

                        <field name="description" placeholder="Description détaillée du poste budgétaire"/>

                        <field name="has_children" invisible="1"/>
                        <notebook invisible="not has_children">
                            <page string="Postes Enfants" name="children">
                                <field name="child_ids" readonly="1">
                                    <tree>
                                        <field name="complete_code"/>
                                        <field name="name"/>
                                        <field name="nature" widget="badge"/>
                                        <field name="budget_type" widget="badge"/>
                                        <field name="is_analytical" widget="boolean_toggle"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Vue kanban de la nomenclature -->
        <record id="view_budget_nomenclature_kanban" model="ir.ui.view">
            <field name="name">budget.nomenclature.kanban</field>
            <field name="model">budget.nomenclature</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="complete_code"/>
                    <field name="name"/>
                    <field name="nature"/>
                    <field name="budget_type"/>
                    <field name="is_analytical"/>
                    <field name="level"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="complete_code"/> - <field name="name"/>
                                        </strong>
                                    </div>
                                    <span class="o_kanban_record_top_right">
                                        <field name="budget_type" widget="label_selection"
                                               options="{'classes': {'expense': 'info', 'revenue': 'success'}}"/>
                                    </span>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="row">
                                        <div class="col-6">
                                            <field name="nature" widget="badge"/>
                                        </div>
                                        <div class="col-6">
                                            <t t-if="record.is_analytical.raw_value">
                                                <span class="badge badge-success">Analytique</span>
                                            </t>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Vue recherche de la nomenclature -->
        <record id="view_budget_nomenclature_search" model="ir.ui.view">
            <field name="name">budget.nomenclature.search</field>
            <field name="model">budget.nomenclature</field>
            <field name="arch" type="xml">
                <search string="Rechercher Postes Budgétaires">
                    <field name="name" string="Nom ou Code" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                    <field name="complete_code"/>
                    <field name="parent_id"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <separator/>
                    <filter string="Dépenses" name="expense" domain="[('budget_type', '=', 'expense')]"/>
                    <filter string="Recettes" name="revenue" domain="[('budget_type', '=', 'revenue')]"/>
                    <separator/>
                    <filter string="Analytiques" name="analytical" domain="[('is_analytical', '=', True)]"/>
                    <filter string="Non analytiques" name="non_analytical" domain="[('is_analytical', '=', False)]"/>
                    <separator/>
                    <filter string="Sections" name="sections" domain="[('nature', '=', 'section')]"/>
                    <filter string="Chapitres" name="chapters" domain="[('nature', '=', 'chapter')]"/>
                    <filter string="Articles" name="articles" domain="[('nature', '=', 'article')]"/>
                    <filter string="Paragraphes" name="paragraphs" domain="[('nature', '=', 'paragraph')]"/>
                    <filter string="Lignes" name="lines" domain="[('nature', '=', 'line')]"/>
                    <separator/>
                    <filter string="Archivé" name="inactive" domain="[('active', '=', False)]"/>
                    <separator/>
                    <group expand="0" string="Grouper par">
                        <filter string="Type budgétaire" name="group_budget_type" context="{'group_by': 'budget_type'}"/>
                        <filter string="Nature" name="group_nature" context="{'group_by': 'nature'}"/>
                        <filter string="Parent" name="group_parent" context="{'group_by': 'parent_id'}"/>
                        <filter string="Société" name="group_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Action pour la nomenclature budgétaire -->
        <record id="action_budget_nomenclature" model="ir.actions.act_window">
            <field name="name">Nomenclature Budgétaire</field>
            <field name="res_model">budget.nomenclature</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_nomenclature_search"/>
            <field name="context">{'search_default_expense': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Créer un nouveau poste budgétaire
                </p>
                <p>
                    La nomenclature budgétaire définit la structure hiérarchique
                    des postes de dépenses et de recettes de votre établissement.
                </p>
                <p>
                    Seuls les postes marqués comme "analytiques" peuvent recevoir
                    des crédits et des engagements.
                </p>
            </field>
        </record>

        <!-- Action pour les postes de dépenses -->
        <record id="action_budget_nomenclature_expense" model="ir.actions.act_window">
            <field name="name">Postes de Dépenses</field>
            <field name="res_model">budget.nomenclature</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_nomenclature_search"/>
            <field name="domain">[('budget_type', '=', 'expense')]</field>
            <field name="context">{'default_budget_type': 'expense', 'search_default_expense': 1}</field>
        </record>

        <!-- Action pour les postes de recettes -->
        <record id="action_budget_nomenclature_revenue" model="ir.actions.act_window">
            <field name="name">Postes de Recettes</field>
            <field name="res_model">budget.nomenclature</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_nomenclature_search"/>
            <field name="domain">[('budget_type', '=', 'revenue')]</field>
            <field name="context">{'default_budget_type': 'revenue', 'search_default_revenue': 1}</field>
        </record>

    </data>
</odoo>
