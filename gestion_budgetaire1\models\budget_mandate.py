# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime
import logging

_logger = logging.getLogger(__name__)


class BudgetMandate(models.Model):
    """Modèle pour la gestion des mandats de paiement"""
    
    _name = 'budget.mandate'
    _description = 'Mandat de Paiement'
    _order = 'date desc, name desc'
    _check_company_auto = True
    _inherit = ['mail.thread', 'mail.activity.mixin']
    
    # Champs de base
    name = fields.Char(
        string='Référence',
        required=True,
        copy=False,
        readonly=True,
        default=lambda self: _('Nouveau'),
        help='Référence unique du mandat'
    )
    
    date = fields.Date(
        string='Date du mandat',
        required=True,
        default=fields.Date.context_today,
        tracking=True,
        help='Date de création du mandat'
    )
    
    payment_date = fields.Date(
        string='Date de paiement',
        tracking=True,
        help='Date effective du paiement'
    )
    
    # Engagement lié
    engagement_id = fields.Many2one(
        'budget.engagement',
        string='Engagement',
        required=True,
        domain="[('state', 'in', ['validated', 'partially_mandated']), ('company_id', '=', company_id)]",
        help='Engagement budgétaire source'
    )
    
    exercise_id = fields.Many2one(
        'budget.exercise',
        string='Exercice budgétaire',
        related='engagement_id.exercise_id',
        store=True,
        readonly=True,
        help='Exercice budgétaire'
    )
    
    nomenclature_id = fields.Many2one(
        'budget.nomenclature',
        string='Poste budgétaire',
        related='engagement_id.nomenclature_id',
        store=True,
        readonly=True,
        help='Poste budgétaire'
    )
    
    # Montant et devise
    amount = fields.Monetary(
        string='Montant',
        currency_field='currency_id',
        required=True,
        tracking=True,
        help='Montant du mandat'
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Société',
        related='engagement_id.company_id',
        store=True,
        readonly=True,
        help='Société concernée'
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Devise',
        related='company_id.currency_id',
        store=True,
        readonly=True
    )
    
    # Bénéficiaire
    partner_id = fields.Many2one(
        'res.partner',
        string='Bénéficiaire',
        required=True,
        help='Bénéficiaire du paiement'
    )
    
    partner_bank_id = fields.Many2one(
        'res.partner.bank',
        string='Compte bancaire',
        domain="[('partner_id', '=', partner_id)]",
        help='Compte bancaire du bénéficiaire'
    )
    
    # Type de paiement
    payment_type_id = fields.Many2one(
        'budget.payment.type',
        string='Type de paiement',
        required=True,
        domain="[('company_id', '=', company_id)]",
        help='Type de paiement/mandat'
    )
    
    # État et workflow
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('waiting_validation', 'En attente de validation'),
        ('validated', 'Validé'),
        ('paid', 'Payé'),
        ('cancelled', 'Annulé'),
    ], string='État', default='draft', required=True, tracking=True,
       help='État du mandat')
    
    # Documents liés
    invoice_id = fields.Many2one(
        'account.move',
        string='Facture',
        help='Facture liée au mandat'
    )
    
    payment_id = fields.Many2one(
        'account.payment',
        string='Paiement comptable',
        help='Paiement comptable généré'
    )
    
    # Description
    description = fields.Text(
        string='Description',
        required=True,
        help='Description du mandat'
    )
    
    payment_reference = fields.Char(
        string='Référence de paiement',
        help='Référence externe du paiement'
    )
    
    # Champs de suivi
    user_id = fields.Many2one(
        'res.users',
        string='Responsable',
        default=lambda self: self.env.user,
        help='Utilisateur responsable du mandat'
    )
    
    validated_by = fields.Many2one(
        'res.users',
        string='Validé par',
        readonly=True,
        help='Utilisateur ayant validé le mandat'
    )
    
    validated_date = fields.Datetime(
        string='Date de validation',
        readonly=True,
        help='Date et heure de validation'
    )
    
    paid_by = fields.Many2one(
        'res.users',
        string='Payé par',
        readonly=True,
        help='Utilisateur ayant marqué le paiement'
    )
    
    paid_date = fields.Datetime(
        string='Date de paiement comptable',
        readonly=True,
        help='Date et heure du paiement'
    )
    
    # Contraintes
    @api.constrains('amount')
    def _check_amount_positive(self):
        """Vérifier que le montant est positif"""
        for record in self:
            if record.amount <= 0:
                raise ValidationError(
                    _('Le montant du mandat doit être positif.')
                )
    
    @api.constrains('amount', 'engagement_id')
    def _check_amount_vs_engagement(self):
        """Vérifier que le montant ne dépasse pas le reste à mandater"""
        for record in self:
            if record.engagement_id:
                # Calculer le total des autres mandats de cet engagement
                other_mandates_amount = sum(
                    mandate.amount for mandate in record.engagement_id.mandate_ids
                    if mandate.id != record.id and mandate.state != 'cancelled'
                )
                
                total_mandated = other_mandates_amount + record.amount
                
                if total_mandated > record.engagement_id.amount:
                    raise ValidationError(
                        _('Le montant total des mandats (%s) dépasse le montant de l\'engagement (%s).') %
                        (total_mandated, record.engagement_id.amount)
                    )
    
    @api.constrains('date', 'exercise_id')
    def _check_date_in_exercise(self):
        """Vérifier que la date est dans l'exercice"""
        for record in self:
            if record.exercise_id and not (record.exercise_id.date_start <= record.date <= record.exercise_id.date_end):
                raise ValidationError(
                    _('La date du mandat doit être comprise dans l\'exercice budgétaire.')
                )
    
    @api.constrains('payment_type_id', 'partner_bank_id')
    def _check_bank_account_requirement(self):
        """Vérifier l'exigence de compte bancaire selon le type de paiement"""
        for record in self:
            if record.payment_type_id.requires_bank_account and not record.partner_bank_id:
                raise ValidationError(
                    _('Un compte bancaire est requis pour le type de paiement "%s".') %
                    record.payment_type_id.name
                )
    
    @api.constrains('amount', 'payment_type_id')
    def _check_max_amount(self):
        """Vérifier le montant maximum selon le type de paiement"""
        for record in self:
            if record.payment_type_id.max_amount and record.amount > record.payment_type_id.max_amount:
                raise ValidationError(
                    _('Le montant (%s) dépasse le maximum autorisé (%s) pour le type de paiement "%s".') %
                    (record.amount, record.payment_type_id.max_amount, record.payment_type_id.name)
                )
    
    # Méthodes CRUD
    @api.model_create_multi
    def create(self, vals_list):
        """Créer un mandat avec numérotation automatique"""
        for vals in vals_list:
            if vals.get('name', _('Nouveau')) == _('Nouveau'):
                vals['name'] = self.env['ir.sequence'].next_by_code('budget.mandate') or _('Nouveau')
        
        return super().create(vals_list)
    
    # Actions de workflow
    def action_submit_for_validation(self):
        """Soumettre pour validation"""
        for record in self:
            if record.state != 'draft':
                raise UserError(_('Seul un mandat en brouillon peut être soumis.'))
            record.state = 'waiting_validation'
    
    def action_validate(self):
        """Valider le mandat"""
        for record in self:
            if record.state != 'waiting_validation':
                raise UserError(_('Seul un mandat en attente peut être validé.'))
            
            record.write({
                'state': 'validated',
                'validated_by': self.env.user.id,
                'validated_date': datetime.now(),
            })
            
            _logger.info('Mandat %s validé par %s', record.name, self.env.user.name)
    
    def action_mark_as_paid(self):
        """Marquer comme payé"""
        for record in self:
            if record.state != 'validated':
                raise UserError(_('Seul un mandat validé peut être marqué comme payé.'))
            
            record.write({
                'state': 'paid',
                'paid_by': self.env.user.id,
                'paid_date': datetime.now(),
                'payment_date': fields.Date.context_today(self),
            })
            
            # Mettre à jour l'état de l'engagement
            record.engagement_id._onchange_state_from_mandates()
            
            _logger.info('Mandat %s marqué comme payé par %s', record.name, self.env.user.name)
    
    def action_cancel(self):
        """Annuler le mandat"""
        for record in self:
            if record.state == 'paid':
                raise UserError(_('Un mandat payé ne peut pas être annulé.'))
            
            record.state = 'cancelled'
            
            # Mettre à jour l'état de l'engagement
            record.engagement_id._onchange_state_from_mandates()
            
            _logger.info('Mandat %s annulé', record.name)
    
    def action_reset_to_draft(self):
        """Remettre en brouillon"""
        for record in self:
            if record.state not in ('waiting_validation', 'cancelled'):
                raise UserError(_('Seul un mandat en attente ou annulé peut être remis en brouillon.'))
            record.state = 'draft'
    
    def action_create_payment(self):
        """Créer un paiement comptable"""
        self.ensure_one()
        
        if self.state != 'validated':
            raise UserError(_('Seul un mandat validé peut générer un paiement comptable.'))
        
        if self.payment_id:
            raise UserError(_('Un paiement comptable existe déjà pour ce mandat.'))
        
        # Préparer les valeurs du paiement
        payment_vals = {
            'payment_type': 'outbound',
            'partner_type': 'supplier',
            'partner_id': self.partner_id.id,
            'amount': self.amount,
            'currency_id': self.currency_id.id,
            'date': self.payment_date or fields.Date.context_today(self),
            'ref': f"{self.name} - {self.description}",
            'partner_bank_id': self.partner_bank_id.id if self.partner_bank_id else False,
        }
        
        # Créer le paiement
        payment = self.env['account.payment'].create(payment_vals)
        self.payment_id = payment.id
        
        return {
            'name': _('Paiement comptable'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.payment',
            'res_id': payment.id,
            'view_mode': 'form',
            'target': 'current',
        }
    
    # Actions de vue
    def action_view_engagement(self):
        """Voir l'engagement source"""
        self.ensure_one()
        return {
            'name': _('Engagement'),
            'type': 'ir.actions.act_window',
            'res_model': 'budget.engagement',
            'res_id': self.engagement_id.id,
            'view_mode': 'form',
            'target': 'current',
        }
    
    def action_view_payment(self):
        """Voir le paiement comptable"""
        self.ensure_one()
        if not self.payment_id:
            raise UserError(_('Aucun paiement comptable associé à ce mandat.'))
        
        return {
            'name': _('Paiement comptable'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.payment',
            'res_id': self.payment_id.id,
            'view_mode': 'form',
            'target': 'current',
        }
    
    # Méthodes utilitaires
    @api.onchange('engagement_id')
    def _onchange_engagement_id(self):
        """Remplir automatiquement les champs depuis l'engagement"""
        if self.engagement_id:
            self.partner_id = self.engagement_id.partner_id
            if not self.description:
                self.description = f"Mandat pour {self.engagement_id.description}"
            
            # Proposer le montant restant à mandater
            if self.engagement_id.amount_remaining > 0:
                self.amount = self.engagement_id.amount_remaining
    
    @api.onchange('partner_id')
    def _onchange_partner_id(self):
        """Mettre à jour le domaine des comptes bancaires"""
        if self.partner_id:
            # Sélectionner automatiquement le premier compte bancaire si un seul existe
            bank_accounts = self.partner_id.bank_ids
            if len(bank_accounts) == 1:
                self.partner_bank_id = bank_accounts[0]
            else:
                self.partner_bank_id = False
    
    @api.onchange('payment_type_id')
    def _onchange_payment_type_id(self):
        """Vérifications selon le type de paiement"""
        if self.payment_type_id:
            # Vérifier le montant maximum
            if self.payment_type_id.max_amount and self.amount > self.payment_type_id.max_amount:
                return {
                    'warning': {
                        'title': _('Attention'),
                        'message': _('Le montant dépasse le maximum autorisé (%s) pour ce type de paiement.') %
                                 self.payment_type_id.max_amount
                    }
                }
            
            # Vérifier l'exigence de compte bancaire
            if self.payment_type_id.requires_bank_account and not self.partner_bank_id:
                return {
                    'warning': {
                        'title': _('Attention'),
                        'message': _('Un compte bancaire est requis pour ce type de paiement.')
                    }
                }
