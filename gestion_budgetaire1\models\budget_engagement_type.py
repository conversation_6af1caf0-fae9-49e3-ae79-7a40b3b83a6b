# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class BudgetEngagementType(models.Model):
    """Modèle pour les types d'engagements budgétaires"""
    
    _name = 'budget.engagement.type'
    _description = 'Type d\'Engagement Budgétaire'
    _order = 'sequence, name'
    _check_company_auto = True
    
    # Champs de base
    name = fields.Char(
        string='Nom',
        required=True,
        translate=True,
        help='Nom du type d\'engagement'
    )
    
    code = fields.Char(
        string='Code',
        required=True,
        size=10,
        help='Code unique du type d\'engagement'
    )
    
    sequence = fields.Integer(
        string='Séquence',
        default=10,
        help='Ordre d\'affichage'
    )
    
    active = fields.Boolean(
        string='Actif',
        default=True,
        help='Décocher pour archiver ce type d\'engagement'
    )
    
    # Impact budgétaire
    budget_impact = fields.Selection([
        ('consumption', 'Consommation'),
        ('liberation', 'Libération'),
        ('neutral', 'Neutre'),
    ], string='Impact budgétaire', required=True, default='consumption',
       help='Impact de ce type d\'engagement sur le budget')
    
    impact_sign = fields.Float(
        string='Signe d\'impact',
        compute='_compute_impact_sign',
        store=True,
        help='Signe mathématique de l\'impact (+1, -1, 0)'
    )
    
    # Configuration
    requires_validation = fields.Boolean(
        string='Nécessite validation',
        default=True,
        help='Cocher si ce type d\'engagement nécessite une validation'
    )
    
    auto_create_mandate = fields.Boolean(
        string='Créer mandat automatiquement',
        default=False,
        help='Créer automatiquement un mandat lors de la validation'
    )
    
    allow_negative_amount = fields.Boolean(
        string='Autoriser montant négatif',
        default=False,
        help='Autoriser les montants négatifs pour ce type'
    )
    
    # Société
    company_id = fields.Many2one(
        'res.company',
        string='Société',
        required=True,
        default=lambda self: self.env.company,
        help='Société concernée'
    )
    
    # Description
    description = fields.Text(
        string='Description',
        translate=True,
        help='Description détaillée du type d\'engagement'
    )
    
    # Champs informatifs
    color = fields.Integer(
        string='Couleur',
        default=0,
        help='Couleur pour l\'affichage dans l\'interface'
    )
    
    # Relations
    engagement_ids = fields.One2many(
        'budget.engagement',
        'engagement_type_id',
        string='Engagements',
        help='Engagements de ce type'
    )
    
    # Contraintes
    @api.constrains('code', 'company_id')
    def _check_unique_code(self):
        """Vérifier l'unicité du code par société"""
        for record in self:
            existing = self.search([
                ('code', '=', record.code),
                ('company_id', '=', record.company_id.id),
                ('id', '!=', record.id)
            ])
            if existing:
                raise ValidationError(
                    _('Le code "%s" existe déjà pour cette société.') % record.code
                )
    
    # Méthodes de calcul
    @api.depends('budget_impact')
    def _compute_impact_sign(self):
        """Calculer le signe d'impact selon le type"""
        for record in self:
            if record.budget_impact == 'consumption':
                record.impact_sign = 1.0
            elif record.budget_impact == 'liberation':
                record.impact_sign = -1.0
            else:  # neutral
                record.impact_sign = 0.0
    
    # Méthodes utilitaires
    def get_impact_amount(self, base_amount):
        """Calculer le montant d'impact selon le type"""
        self.ensure_one()
        return base_amount * self.impact_sign
    
    def name_get(self):
        """Personnaliser l'affichage du nom"""
        result = []
        for record in self:
            name = f"[{record.code}] {record.name}"
            result.append((record.id, name))
        return result
    
    @api.model
    def get_default_types(self):
        """Obtenir les types d'engagement par défaut"""
        return {
            'prise_en_charge': {
                'name': 'Prise en charge',
                'code': 'PEC',
                'budget_impact': 'consumption',
                'requires_validation': True,
                'auto_create_mandate': False,
            },
            'depense': {
                'name': 'Dépense',
                'code': 'DEP',
                'budget_impact': 'consumption',
                'requires_validation': True,
                'auto_create_mandate': True,
            },
            'economie': {
                'name': 'Économie',
                'code': 'ECO',
                'budget_impact': 'liberation',
                'requires_validation': True,
                'auto_create_mandate': False,
                'allow_negative_amount': True,
            },
            'avoir': {
                'name': 'Avoir',
                'code': 'AVO',
                'budget_impact': 'liberation',
                'requires_validation': True,
                'auto_create_mandate': False,
                'allow_negative_amount': True,
            },
            'transfert': {
                'name': 'Transfert de crédits',
                'code': 'TRA',
                'budget_impact': 'neutral',
                'requires_validation': True,
                'auto_create_mandate': False,
            },
            'rattachement': {
                'name': 'Rattachement de crédits',
                'code': 'RAT',
                'budget_impact': 'neutral',
                'requires_validation': True,
                'auto_create_mandate': False,
            },
        }


class BudgetPaymentType(models.Model):
    """Modèle pour les types de paiements/mandats"""
    
    _name = 'budget.payment.type'
    _description = 'Type de Paiement/Mandat'
    _order = 'sequence, name'
    _check_company_auto = True
    
    # Champs de base
    name = fields.Char(
        string='Nom',
        required=True,
        translate=True,
        help='Nom du type de paiement'
    )
    
    code = fields.Char(
        string='Code',
        required=True,
        size=10,
        help='Code unique du type de paiement'
    )
    
    sequence = fields.Integer(
        string='Séquence',
        default=10,
        help='Ordre d\'affichage'
    )
    
    active = fields.Boolean(
        string='Actif',
        default=True,
        help='Décocher pour archiver ce type de paiement'
    )
    
    # Configuration
    requires_bank_account = fields.Boolean(
        string='Nécessite compte bancaire',
        default=False,
        help='Cocher si ce type de paiement nécessite un compte bancaire'
    )
    
    is_electronic = fields.Boolean(
        string='Paiement électronique',
        default=False,
        help='Cocher si c\'est un paiement électronique'
    )
    
    max_amount = fields.Monetary(
        string='Montant maximum',
        currency_field='currency_id',
        help='Montant maximum autorisé pour ce type de paiement'
    )
    
    # Société et devise
    company_id = fields.Many2one(
        'res.company',
        string='Société',
        required=True,
        default=lambda self: self.env.company,
        help='Société concernée'
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Devise',
        related='company_id.currency_id',
        store=True,
        readonly=True
    )
    
    # Description
    description = fields.Text(
        string='Description',
        translate=True,
        help='Description détaillée du type de paiement'
    )
    
    # Relations
    mandate_ids = fields.One2many(
        'budget.mandate',
        'payment_type_id',
        string='Mandats',
        help='Mandats de ce type'
    )
    
    # Contraintes
    @api.constrains('code', 'company_id')
    def _check_unique_code(self):
        """Vérifier l'unicité du code par société"""
        for record in self:
            existing = self.search([
                ('code', '=', record.code),
                ('company_id', '=', record.company_id.id),
                ('id', '!=', record.id)
            ])
            if existing:
                raise ValidationError(
                    _('Le code "%s" existe déjà pour cette société.') % record.code
                )
    
    @api.constrains('max_amount')
    def _check_max_amount(self):
        """Vérifier que le montant maximum est positif"""
        for record in self:
            if record.max_amount and record.max_amount <= 0:
                raise ValidationError(
                    _('Le montant maximum doit être positif.')
                )
    
    def name_get(self):
        """Personnaliser l'affichage du nom"""
        result = []
        for record in self:
            name = f"[{record.code}] {record.name}"
            result.append((record.id, name))
        return result
    
    @api.model
    def get_default_types(self):
        """Obtenir les types de paiement par défaut"""
        return {
            'ccp': {
                'name': 'CCP (Compte Courant Postal)',
                'code': 'CCP',
                'requires_bank_account': True,
                'is_electronic': True,
            },
            'tresor': {
                'name': 'Trésor Public',
                'code': 'TRE',
                'requires_bank_account': False,
                'is_electronic': False,
            },
            'banque': {
                'name': 'Banque',
                'code': 'BAN',
                'requires_bank_account': True,
                'is_electronic': True,
            },
            'especes': {
                'name': 'Espèces',
                'code': 'ESP',
                'requires_bank_account': False,
                'is_electronic': False,
                'max_amount': 50000.0,  # Exemple de limite
            },
            'cheque': {
                'name': 'Chèque',
                'code': 'CHQ',
                'requires_bank_account': True,
                'is_electronic': False,
            },
        }
