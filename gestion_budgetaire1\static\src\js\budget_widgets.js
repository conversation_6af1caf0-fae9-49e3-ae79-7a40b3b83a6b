/** @odoo-module **/

import { Component, useState } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

/**
 * Widget pour afficher une barre de progression budgétaire
 */
export class BudgetProgressWidget extends Component {
    static template = "gestion_budgetaire.BudgetProgressWidget";
    static props = {
        value: { type: Number },
        max: { type: Number },
        label: { type: String, optional: true },
        showPercentage: { type: Boolean, optional: true },
        thresholds: { type: Object, optional: true },
    };

    setup() {
        this.state = useState({
            percentage: this.getPercentage(),
            status: this.getStatus(),
        });
    }

    getPercentage() {
        if (!this.props.max || this.props.max === 0) return 0;
        return Math.min((this.props.value / this.props.max) * 100, 100);
    }

    getStatus() {
        const percentage = this.getPercentage();
        const thresholds = this.props.thresholds || { warning: 80, danger: 100 };
        
        if (percentage >= thresholds.danger) return 'danger';
        if (percentage >= thresholds.warning) return 'warning';
        return 'normal';
    }

    getProgressBarClass() {
        return `budget-progress-bar ${this.state.status}`;
    }

    getProgressBarStyle() {
        return `width: ${this.state.percentage}%`;
    }

    getDisplayText() {
        if (this.props.showPercentage) {
            return `${this.state.percentage.toFixed(1)}%`;
        }
        return this.props.label || '';
    }
}

/**
 * Widget pour afficher un indicateur budgétaire
 */
export class BudgetIndicatorWidget extends Component {
    static template = "gestion_budgetaire.BudgetIndicatorWidget";
    static props = {
        amount: { type: Number },
        currency: { type: String, optional: true },
        status: { type: String, optional: true },
        label: { type: String, optional: true },
    };

    getIndicatorClass() {
        const status = this.props.status || this.getStatusFromAmount();
        return `budget-indicator ${status}`;
    }

    getStatusFromAmount() {
        if (this.props.amount < 0) return 'exceeded';
        if (this.props.amount < 1000) return 'alert';
        return 'available';
    }

    getFormattedAmount() {
        const amount = this.props.amount || 0;
        const currency = this.props.currency || 'DZD';
        
        return new Intl.NumberFormat('fr-DZ', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(amount);
    }
}

/**
 * Widget pour afficher une carte budgétaire
 */
export class BudgetCardWidget extends Component {
    static template = "gestion_budgetaire.BudgetCardWidget";
    static props = {
        title: { type: String },
        subtitle: { type: String, optional: true },
        amounts: { type: Array },
        actions: { type: Array, optional: true },
        status: { type: String, optional: true },
    };

    getCardClass() {
        const status = this.props.status || 'normal';
        return `budget-card ${status}`;
    }

    getAmountClass(amount) {
        if (amount.value < 0) return 'budget-amount-value negative';
        if (amount.positive) return 'budget-amount-value positive';
        return 'budget-amount-value';
    }

    formatAmount(value, currency = 'DZD') {
        return new Intl.NumberFormat('fr-DZ', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(value);
    }
}

/**
 * Service pour les calculs budgétaires
 */
export class BudgetCalculationService {
    constructor() {
        this.name = "budget_calculation";
    }

    /**
     * Calculer le taux de consommation
     */
    calculateConsumptionRate(engaged, voted) {
        if (!voted || voted === 0) return 0;
        return (engaged / voted) * 100;
    }

    /**
     * Calculer le montant disponible
     */
    calculateAvailableAmount(voted, engaged) {
        return voted - engaged;
    }

    /**
     * Calculer le taux de paiement
     */
    calculatePaymentRate(paid, engaged) {
        if (!engaged || engaged === 0) return 0;
        return (paid / engaged) * 100;
    }

    /**
     * Déterminer le statut budgétaire
     */
    getBudgetStatus(consumptionRate, thresholds = { warning: 80, danger: 100 }) {
        if (consumptionRate >= thresholds.danger) return 'danger';
        if (consumptionRate >= thresholds.warning) return 'warning';
        return 'normal';
    }

    /**
     * Formater un montant
     */
    formatAmount(amount, currency = 'DZD', locale = 'fr-DZ') {
        return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(amount);
    }

    /**
     * Valider la disponibilité budgétaire
     */
    checkBudgetAvailability(requestedAmount, availableAmount) {
        return {
            available: availableAmount >= requestedAmount,
            shortage: Math.max(0, requestedAmount - availableAmount),
            percentage: availableAmount > 0 ? (requestedAmount / availableAmount) * 100 : 0,
        };
    }
}

/**
 * Composant pour les alertes budgétaires
 */
export class BudgetAlertComponent extends Component {
    static template = "gestion_budgetaire.BudgetAlertComponent";
    static props = {
        type: { type: String }, // info, warning, danger, success
        title: { type: String, optional: true },
        message: { type: String },
        dismissible: { type: Boolean, optional: true },
        actions: { type: Array, optional: true },
    };

    setup() {
        this.state = useState({
            visible: true,
        });
    }

    getAlertClass() {
        return `budget-alert ${this.props.type}`;
    }

    dismiss() {
        this.state.visible = false;
    }

    getIcon() {
        const icons = {
            info: 'fa-info-circle',
            warning: 'fa-exclamation-triangle',
            danger: 'fa-times-circle',
            success: 'fa-check-circle',
        };
        return icons[this.props.type] || 'fa-info-circle';
    }
}

// Enregistrement des widgets et services
registry.category("services").add("budget_calculation", BudgetCalculationService);

// Templates Owl
registry.category("web_components").add("BudgetProgressWidget", BudgetProgressWidget);
registry.category("web_components").add("BudgetIndicatorWidget", BudgetIndicatorWidget);
registry.category("web_components").add("BudgetCardWidget", BudgetCardWidget);
registry.category("web_components").add("BudgetAlertComponent", BudgetAlertComponent);

// Fonctions utilitaires globales
window.BudgetUtils = {
    /**
     * Formater un montant en devise algérienne
     */
    formatDZD: function(amount) {
        return new Intl.NumberFormat('fr-DZ', {
            style: 'currency',
            currency: 'DZD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(amount);
    },

    /**
     * Calculer le pourcentage
     */
    calculatePercentage: function(value, total) {
        if (!total || total === 0) return 0;
        return (value / total) * 100;
    },

    /**
     * Obtenir la classe CSS selon le statut
     */
    getStatusClass: function(percentage, thresholds = { warning: 80, danger: 100 }) {
        if (percentage >= thresholds.danger) return 'danger';
        if (percentage >= thresholds.warning) return 'warning';
        return 'normal';
    },

    /**
     * Créer une barre de progression
     */
    createProgressBar: function(container, value, max, options = {}) {
        const percentage = this.calculatePercentage(value, max);
        const status = this.getStatusClass(percentage, options.thresholds);
        
        const progressBar = document.createElement('div');
        progressBar.className = 'budget-progress';
        
        const progressFill = document.createElement('div');
        progressFill.className = `budget-progress-bar ${status}`;
        progressFill.style.width = `${Math.min(percentage, 100)}%`;
        
        if (options.showText) {
            progressFill.textContent = options.showPercentage ? 
                `${percentage.toFixed(1)}%` : 
                this.formatDZD(value);
        }
        
        progressBar.appendChild(progressFill);
        container.appendChild(progressBar);
        
        return progressBar;
    },

    /**
     * Afficher une notification budgétaire
     */
    showBudgetNotification: function(message, type = 'info', duration = 5000) {
        // Utiliser le système de notification d'Odoo si disponible
        if (window.odoo && window.odoo.notification) {
            window.odoo.notification.add(message, { type: type });
        } else {
            // Fallback vers une notification simple
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    },

    /**
     * Valider un montant budgétaire
     */
    validateBudgetAmount: function(amount, available, options = {}) {
        const result = {
            valid: true,
            warnings: [],
            errors: [],
        };

        if (amount <= 0) {
            result.valid = false;
            result.errors.push('Le montant doit être positif');
        }

        if (amount > available) {
            if (options.strict) {
                result.valid = false;
                result.errors.push(`Crédit insuffisant. Disponible: ${this.formatDZD(available)}`);
            } else {
                result.warnings.push(`Attention: dépassement de crédit de ${this.formatDZD(amount - available)}`);
            }
        }

        const percentage = this.calculatePercentage(amount, available);
        if (percentage >= 80 && percentage < 100) {
            result.warnings.push('Seuil d\'alerte atteint (80%)');
        }

        return result;
    }
};
