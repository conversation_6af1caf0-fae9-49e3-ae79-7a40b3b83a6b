<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Types d'engagement par défaut -->
        <record id="engagement_type_pec" model="budget.engagement.type">
            <field name="name">Prise en charge</field>
            <field name="code">PEC</field>
            <field name="sequence">10</field>
            <field name="budget_impact">consumption</field>
            <field name="requires_validation">True</field>
            <field name="auto_create_mandate">False</field>
            <field name="allow_negative_amount">False</field>
            <field name="description">Engagement pour prise en charge de dépenses</field>
            <field name="color">1</field>
        </record>
        
        <record id="engagement_type_depense" model="budget.engagement.type">
            <field name="name">Dépense</field>
            <field name="code">DEP</field>
            <field name="sequence">20</field>
            <field name="budget_impact">consumption</field>
            <field name="requires_validation">True</field>
            <field name="auto_create_mandate">True</field>
            <field name="allow_negative_amount">False</field>
            <field name="description">Engagement pour dépense directe</field>
            <field name="color">2</field>
        </record>
        
        <record id="engagement_type_economie" model="budget.engagement.type">
            <field name="name">Économie</field>
            <field name="code">ECO</field>
            <field name="sequence">30</field>
            <field name="budget_impact">liberation</field>
            <field name="requires_validation">True</field>
            <field name="auto_create_mandate">False</field>
            <field name="allow_negative_amount">True</field>
            <field name="description">Économie sur engagement existant</field>
            <field name="color">3</field>
        </record>
        
        <record id="engagement_type_avoir" model="budget.engagement.type">
            <field name="name">Avoir</field>
            <field name="code">AVO</field>
            <field name="sequence">40</field>
            <field name="budget_impact">liberation</field>
            <field name="requires_validation">True</field>
            <field name="auto_create_mandate">False</field>
            <field name="allow_negative_amount">True</field>
            <field name="description">Avoir ou annulation partielle d'engagement</field>
            <field name="color">4</field>
        </record>
        
        <record id="engagement_type_transfert" model="budget.engagement.type">
            <field name="name">Transfert de crédits</field>
            <field name="code">TRA</field>
            <field name="sequence">50</field>
            <field name="budget_impact">neutral</field>
            <field name="requires_validation">True</field>
            <field name="auto_create_mandate">False</field>
            <field name="allow_negative_amount">False</field>
            <field name="description">Transfert de crédits entre postes</field>
            <field name="color">5</field>
        </record>
        
        <record id="engagement_type_rattachement" model="budget.engagement.type">
            <field name="name">Rattachement de crédits</field>
            <field name="code">RAT</field>
            <field name="sequence">60</field>
            <field name="budget_impact">neutral</field>
            <field name="requires_validation">True</field>
            <field name="auto_create_mandate">False</field>
            <field name="allow_negative_amount">False</field>
            <field name="description">Rattachement de crédits de l'exercice précédent</field>
            <field name="color">6</field>
        </record>
        
        <!-- Types de paiement par défaut -->
        <record id="payment_type_ccp" model="budget.payment.type">
            <field name="name">CCP (Compte Courant Postal)</field>
            <field name="code">CCP</field>
            <field name="sequence">10</field>
            <field name="requires_bank_account">True</field>
            <field name="is_electronic">True</field>
            <field name="description">Paiement par Compte Courant Postal</field>
        </record>
        
        <record id="payment_type_tresor" model="budget.payment.type">
            <field name="name">Trésor Public</field>
            <field name="code">TRE</field>
            <field name="sequence">20</field>
            <field name="requires_bank_account">False</field>
            <field name="is_electronic">False</field>
            <field name="description">Paiement par le Trésor Public</field>
        </record>
        
        <record id="payment_type_banque" model="budget.payment.type">
            <field name="name">Banque</field>
            <field name="code">BAN</field>
            <field name="sequence">30</field>
            <field name="requires_bank_account">True</field>
            <field name="is_electronic">True</field>
            <field name="description">Paiement bancaire</field>
        </record>
        
        <record id="payment_type_especes" model="budget.payment.type">
            <field name="name">Espèces</field>
            <field name="code">ESP</field>
            <field name="sequence">40</field>
            <field name="requires_bank_account">False</field>
            <field name="is_electronic">False</field>
            <field name="max_amount">50000.0</field>
            <field name="description">Paiement en espèces (limité)</field>
        </record>
        
        <record id="payment_type_cheque" model="budget.payment.type">
            <field name="name">Chèque</field>
            <field name="code">CHQ</field>
            <field name="sequence">50</field>
            <field name="requires_bank_account">True</field>
            <field name="is_electronic">False</field>
            <field name="description">Paiement par chèque</field>
        </record>
        
        <!-- Nomenclature budgétaire de base (exemple pour l'Algérie) -->
        <!-- Section Fonctionnement -->
        <record id="nomenclature_section_fonctionnement" model="budget.nomenclature">
            <field name="name">Fonctionnement</field>
            <field name="code">01</field>
            <field name="sequence">10</field>
            <field name="budget_type">expense</field>
            <field name="nature">section</field>
            <field name="is_analytical">False</field>
            <field name="description">Section Fonctionnement - Dépenses de fonctionnement</field>
        </record>
        
        <!-- Chapitre Personnel -->
        <record id="nomenclature_chapitre_personnel" model="budget.nomenclature">
            <field name="name">Personnel</field>
            <field name="code">11</field>
            <field name="sequence">10</field>
            <field name="parent_id" ref="nomenclature_section_fonctionnement"/>
            <field name="budget_type">expense</field>
            <field name="nature">chapter</field>
            <field name="is_analytical">False</field>
            <field name="description">Chapitre Personnel - Dépenses de personnel</field>
        </record>
        
        <!-- Article Salaires -->
        <record id="nomenclature_article_salaires" model="budget.nomenclature">
            <field name="name">Salaires et traitements</field>
            <field name="code">111</field>
            <field name="sequence">10</field>
            <field name="parent_id" ref="nomenclature_chapitre_personnel"/>
            <field name="budget_type">expense</field>
            <field name="nature">article</field>
            <field name="is_analytical">True</field>
            <field name="description">Article Salaires - Salaires et traitements du personnel</field>
        </record>
        
        <!-- Article Charges sociales -->
        <record id="nomenclature_article_charges_sociales" model="budget.nomenclature">
            <field name="name">Charges sociales</field>
            <field name="code">112</field>
            <field name="sequence">20</field>
            <field name="parent_id" ref="nomenclature_chapitre_personnel"/>
            <field name="budget_type">expense</field>
            <field name="nature">article</field>
            <field name="is_analytical">True</field>
            <field name="description">Article Charges sociales - Cotisations et charges sociales</field>
        </record>
        
        <!-- Chapitre Moyens de service -->
        <record id="nomenclature_chapitre_moyens" model="budget.nomenclature">
            <field name="name">Moyens de service</field>
            <field name="code">12</field>
            <field name="sequence">20</field>
            <field name="parent_id" ref="nomenclature_section_fonctionnement"/>
            <field name="budget_type">expense</field>
            <field name="nature">chapter</field>
            <field name="is_analytical">False</field>
            <field name="description">Chapitre Moyens de service - Dépenses de fonctionnement</field>
        </record>
        
        <!-- Article Fournitures -->
        <record id="nomenclature_article_fournitures" model="budget.nomenclature">
            <field name="name">Fournitures</field>
            <field name="code">121</field>
            <field name="sequence">10</field>
            <field name="parent_id" ref="nomenclature_chapitre_moyens"/>
            <field name="budget_type">expense</field>
            <field name="nature">article</field>
            <field name="is_analytical">True</field>
            <field name="description">Article Fournitures - Fournitures de bureau et consommables</field>
        </record>
        
        <!-- Article Services extérieurs -->
        <record id="nomenclature_article_services" model="budget.nomenclature">
            <field name="name">Services extérieurs</field>
            <field name="code">122</field>
            <field name="sequence">20</field>
            <field name="parent_id" ref="nomenclature_chapitre_moyens"/>
            <field name="budget_type">expense</field>
            <field name="nature">article</field>
            <field name="is_analytical">True</field>
            <field name="description">Article Services extérieurs - Prestations de services</field>
        </record>
        
        <!-- Section Équipement -->
        <record id="nomenclature_section_equipement" model="budget.nomenclature">
            <field name="name">Équipement</field>
            <field name="code">02</field>
            <field name="sequence">20</field>
            <field name="budget_type">expense</field>
            <field name="nature">section</field>
            <field name="is_analytical">False</field>
            <field name="description">Section Équipement - Dépenses d'investissement</field>
        </record>
        
        <!-- Chapitre Équipements -->
        <record id="nomenclature_chapitre_equipements" model="budget.nomenclature">
            <field name="name">Équipements</field>
            <field name="code">21</field>
            <field name="sequence">10</field>
            <field name="parent_id" ref="nomenclature_section_equipement"/>
            <field name="budget_type">expense</field>
            <field name="nature">chapter</field>
            <field name="is_analytical">False</field>
            <field name="description">Chapitre Équipements - Acquisition d'équipements</field>
        </record>
        
        <!-- Article Matériel informatique -->
        <record id="nomenclature_article_informatique" model="budget.nomenclature">
            <field name="name">Matériel informatique</field>
            <field name="code">211</field>
            <field name="sequence">10</field>
            <field name="parent_id" ref="nomenclature_chapitre_equipements"/>
            <field name="budget_type">expense</field>
            <field name="nature">article</field>
            <field name="is_analytical">True</field>
            <field name="description">Article Matériel informatique - Ordinateurs, serveurs, etc.</field>
        </record>
        
        <!-- Section Recettes -->
        <record id="nomenclature_section_recettes" model="budget.nomenclature">
            <field name="name">Recettes</field>
            <field name="code">R01</field>
            <field name="sequence">100</field>
            <field name="budget_type">revenue</field>
            <field name="nature">section</field>
            <field name="is_analytical">False</field>
            <field name="description">Section Recettes - Recettes de l'établissement</field>
        </record>
        
        <!-- Chapitre Subventions -->
        <record id="nomenclature_chapitre_subventions" model="budget.nomenclature">
            <field name="name">Subventions</field>
            <field name="code">R11</field>
            <field name="sequence">10</field>
            <field name="parent_id" ref="nomenclature_section_recettes"/>
            <field name="budget_type">revenue</field>
            <field name="nature">chapter</field>
            <field name="is_analytical">False</field>
            <field name="description">Chapitre Subventions - Subventions reçues</field>
        </record>
        
        <!-- Article Subvention de l'État -->
        <record id="nomenclature_article_subvention_etat" model="budget.nomenclature">
            <field name="name">Subvention de l'État</field>
            <field name="code">R111</field>
            <field name="sequence">10</field>
            <field name="parent_id" ref="nomenclature_chapitre_subventions"/>
            <field name="budget_type">revenue</field>
            <field name="nature">article</field>
            <field name="is_analytical">True</field>
            <field name="description">Article Subvention de l'État - Subvention annuelle de l'État</field>
        </record>
        
    </data>
</odoo>
