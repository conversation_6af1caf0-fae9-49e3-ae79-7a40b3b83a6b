# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class ProductTemplate(models.Model):
    """Extension du modèle product.template pour l'imputation budgétaire par défaut"""
    
    _inherit = 'product.template'
    
    # Imputation budgétaire par défaut
    budget_nomenclature_id = fields.Many2one(
        'budget.nomenclature',
        string='Poste budgétaire par défaut',
        domain="[('is_analytical', '=', True)]",
        help='Poste budgétaire proposé par défaut pour ce produit'
    )
    
    budget_nomenclature_expense_id = fields.Many2one(
        'budget.nomenclature',
        string='Poste budgétaire (Dépenses)',
        domain="[('is_analytical', '=', True), ('budget_type', '=', 'expense')]",
        help='Poste budgétaire par défaut pour les dépenses (achats)'
    )
    
    budget_nomenclature_revenue_id = fields.Many2one(
        'budget.nomenclature',
        string='Poste budgétaire (Recettes)',
        domain="[('is_analytical', '=', True), ('budget_type', '=', 'revenue')]",
        help='Poste budgétaire par défaut pour les recettes (ventes)'
    )
    
    # Méthodes utilitaires
    def get_budget_nomenclature(self, operation_type='expense'):
        """Obtenir le poste budgétaire selon le type d'opération"""
        self.ensure_one()
        
        if operation_type == 'expense':
            return self.budget_nomenclature_expense_id or self.budget_nomenclature_id
        elif operation_type == 'revenue':
            return self.budget_nomenclature_revenue_id or self.budget_nomenclature_id
        else:
            return self.budget_nomenclature_id


class ProductProduct(models.Model):
    """Extension du modèle product.product"""
    
    _inherit = 'product.product'
    
    def get_budget_nomenclature(self, operation_type='expense'):
        """Obtenir le poste budgétaire selon le type d'opération"""
        self.ensure_one()
        return self.product_tmpl_id.get_budget_nomenclature(operation_type)
