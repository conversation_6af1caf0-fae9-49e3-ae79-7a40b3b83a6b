# Documentation Technique - Gestion Budgétaire

## Architecture du Module

### Structure des Répertoires

```
gestion_budgetaire/
├── __init__.py                 # Point d'entrée du module
├── __manifest__.py             # Manifeste du module
├── hooks.py                    # Hooks d'installation/désinstallation
├── README.md                   # Documentation utilisateur
├── controllers/                # Contrôleurs web (vide pour l'instant)
├── data/                       # Données de base
│   ├── budget_sequence.xml     # Séquences
│   └── budget_data.xml         # Types par défaut
├── demo/                       # Données de démonstration
│   └── budget_demo.xml         # Jeu de données de test
├── doc/                        # Documentation
│   └── TECHNICAL.md            # Ce fichier
├── migrations/                 # Scripts de migration
│   └── *******.0.0/           # Migration vers version 1.0.0
├── models/                     # Modèles de données
│   ├── __init__.py
│   ├── budget_exercise.py      # Exercices budgétaires
│   ├── budget_nomenclature.py  # Nomenclature budgétaire
│   ├── budget_credit.py        # Crédits budgétaires
│   ├── budget_engagement.py    # Engagements
│   ├── budget_mandate.py       # Mandats de paiement
│   ├── budget_adjustment.py    # Ajustements budgétaires
│   ├── budget_perception_title.py # Titres de perception
│   ├── purchase_order.py       # Extension commandes d'achat
│   ├── product_template.py     # Extension produits
│   └── res_config_settings.py # Paramètres de configuration
├── reports/                    # Rapports
│   ├── __init__.py
│   ├── budget_reports.py       # Modèles de rapports
│   ├── budget_reports.xml      # Vues des rapports
│   └── budget_report_templates.xml # Templates PDF
├── security/                   # Sécurité
│   ├── budget_security.xml     # Groupes et règles
│   └── ir.model.access.csv     # Droits d'accès
├── static/                     # Ressources statiques
│   ├── description/            # Description du module
│   │   ├── icon.png           # Icône du module
│   │   └── index.html         # Page de description
│   └── src/                   # Sources frontend
│       ├── css/
│       │   └── budget.css     # Styles CSS
│       └── js/
│           └── budget_widgets.js # Widgets JavaScript
├── tests/                      # Tests unitaires
│   ├── __init__.py
│   ├── test_budget_exercise.py
│   ├── test_budget_engagement.py
│   └── test_budget_credit.py
├── views/                      # Vues et interfaces
│   ├── budget_exercise_views.xml
│   ├── budget_nomenclature_views.xml
│   ├── budget_credit_views.xml
│   ├── budget_engagement_views.xml
│   ├── budget_mandate_views.xml
│   ├── budget_adjustment_views.xml
│   ├── budget_perception_title_views.xml
│   ├── budget_config_views.xml
│   └── budget_menus.xml        # Structure des menus
└── wizards/                    # Assistants
    ├── __init__.py
    ├── budget_adjustment_wizard.py
    ├── budget_import_wizard.py
    ├── budget_adjustment_wizard_views.xml
    └── budget_import_wizard_views.xml
```

## Modèles de Données

### Hiérarchie des Modèles

```
budget.exercise (Exercice Budgétaire)
├── budget.credit (Crédits Budgétaires)
│   └── budget.nomenclature (Nomenclature)
├── budget.engagement (Engagements)
│   ├── budget.engagement.type (Types d'Engagement)
│   ├── budget.nomenclature (Nomenclature)
│   └── budget.mandate (Mandats)
│       └── budget.payment.type (Types de Paiement)
├── budget.adjustment (Ajustements)
│   └── budget.adjustment.line (Lignes d'Ajustement)
└── budget.perception.title (Titres de Perception)
    └── budget.perception.collection (Encaissements)
```

### Relations Principales

- **Exercice → Crédits** : One2many (un exercice contient plusieurs crédits)
- **Exercice → Engagements** : One2many (un exercice contient plusieurs engagements)
- **Nomenclature → Crédits** : One2many (une nomenclature peut avoir plusieurs crédits)
- **Engagement → Mandats** : One2many (un engagement peut générer plusieurs mandats)
- **Crédit → Engagements** : One2many (un crédit peut avoir plusieurs engagements)

## Workflows

### Workflow des Engagements

```
[Brouillon] → [En attente] → [Validé] → [Part. mandaté] → [Tot. mandaté]
     ↓              ↓            ↓
  [Annulé]      [Annulé]    [Annulé]
```

**États :**
- `draft` : Engagement en cours de saisie
- `waiting_validation` : Soumis pour validation
- `validated` : Validé, peut être mandaté
- `partially_mandated` : Partiellement mandaté
- `fully_mandated` : Totalement mandaté
- `cancelled` : Annulé

**Transitions :**
- `action_submit_for_validation()` : draft → waiting_validation
- `action_validate()` : waiting_validation → validated
- `action_cancel()` : * → cancelled
- `action_reset_to_draft()` : waiting_validation|cancelled → draft

### Workflow des Mandats

```
[Brouillon] → [En attente] → [Validé] → [Payé]
     ↓              ↓           ↓
  [Annulé]      [Annulé]   [Annulé]
```

**États :**
- `draft` : Mandat en cours de saisie
- `waiting_validation` : Soumis pour validation
- `validated` : Validé, prêt pour paiement
- `paid` : Payé
- `cancelled` : Annulé

## Contrôles Budgétaires

### Vérifications Automatiques

1. **Disponibilité des crédits** : Vérification lors de la validation des engagements
2. **Cohérence des dates** : Les dates doivent être dans l'exercice budgétaire
3. **Montants positifs** : Les montants doivent être positifs (sauf exceptions)
4. **Unicité** : Un seul crédit par nomenclature et exercice

### Seuils et Alertes

- **Seuil d'alerte** : Déclenche une alerte (défaut : 80%)
- **Seuil de blocage** : Bloque automatiquement le crédit (défaut : 100%)

## Intégrations

### Module Achats (purchase)

- **Extension de purchase.order** : Ajout de champs budgétaires
- **Extension de purchase.order.line** : Imputation budgétaire
- **Création automatique d'engagements** : Lors de la confirmation des commandes

### Module Ventes (sale)

- **Génération de titres de perception** : Depuis les factures clients

### Module Comptabilité (account)

- **Synchronisation des paiements** : Liaison avec les mandats
- **Intégration analytique** : Liaison avec les comptes analytiques

## Sécurité

### Groupes d'Utilisateurs

1. **group_budget_user** : Consultation
2. **group_budget_agent** : Saisie et modification
3. **group_budget_controller** : Validation
4. **group_budget_manager** : Gestion et configuration
5. **group_budget_admin** : Administration complète

### Règles d'Accès

- **Multi-société** : Accès limité aux données de la société de l'utilisateur
- **Règles par enregistrement** : Contrôle fin des permissions selon les groupes

## Performance

### Optimisations

1. **Champs calculés stockés** : Pour les montants fréquemment consultés
2. **Index de base de données** : Sur les champs de recherche fréquents
3. **Calculs en lot** : Pour les recalculs de montants

### Recommandations

- Utiliser les vues de rapport pour les analyses complexes
- Éviter les calculs en temps réel sur de gros volumes
- Utiliser la pagination pour les listes importantes

## Tests

### Types de Tests

1. **Tests unitaires** : Validation des modèles et méthodes
2. **Tests d'intégration** : Vérification des workflows
3. **Tests de performance** : Sur de gros volumes de données

### Exécution des Tests

```bash
# Tous les tests du module
odoo-bin -d test_db -i gestion_budgetaire --test-enable --stop-after-init

# Tests spécifiques
odoo-bin -d test_db --test-tags gestion_budgetaire --test-enable --stop-after-init
```

## Déploiement

### Prérequis

- Odoo 17.0+
- PostgreSQL 12+
- Python 3.8+

### Installation

1. Copier le module dans le répertoire addons
2. Redémarrer Odoo
3. Installer le module depuis l'interface
4. Configurer les données de base

### Migration

- Scripts de migration fournis dans le répertoire `migrations/`
- Hooks d'installation pour la configuration initiale

## Maintenance

### Logs

- Utilisation du logger Python standard
- Logs détaillés pour les opérations critiques
- Niveaux : DEBUG, INFO, WARNING, ERROR

### Monitoring

- Surveillance des performances des calculs budgétaires
- Alertes sur les dépassements de seuils
- Suivi de l'utilisation des crédits

### Sauvegarde

- Sauvegarde régulière de la base de données
- Export des données budgétaires critiques
- Procédures de restauration documentées

## Développement

### Standards de Code

- Respect des conventions Odoo
- Documentation des méthodes complexes
- Tests unitaires pour les nouvelles fonctionnalités

### Contribution

1. Fork du projet
2. Branche de développement
3. Tests et validation
4. Pull request avec description détaillée

### Roadmap

- Version 1.1 : Tableaux de bord avancés
- Version 1.2 : API REST pour intégrations externes
- Version 1.3 : Module de prévisions budgétaires
