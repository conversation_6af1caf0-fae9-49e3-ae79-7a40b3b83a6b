<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Vue formulaire de l'assistant d'ajustement -->
        <record id="view_budget_adjustment_wizard_form" model="ir.ui.view">
            <field name="name">budget.adjustment.wizard.form</field>
            <field name="model">budget.adjustment.wizard</field>
            <field name="arch" type="xml">
                <form string="Assistant Ajustement Budgétaire">
                    <sheet>
                        <div class="oe_title">
                            <h1>Créer un Ajustement Budgétaire</h1>
                        </div>

                        <group>
                            <group>
                                <field name="exercise_id"/>
                                <field name="adjustment_type"/>
                                <field name="currency_id" invisible="1"/>
                            </group>
                            <group>
                                <field name="total_debit" widget="monetary"/>
                                <field name="total_credit" widget="monetary"/>
                                <field name="balance" widget="monetary"
                                       decoration-danger="balance != 0 and adjustment_type == 'transfer'"/>
                            </group>
                        </group>

                        <group string="Description">
                            <field name="description" nolabel="1" placeholder="Description de l'ajustement"/>
                        </group>

                        <group string="Motif">
                            <field name="reason" nolabel="1" placeholder="Motif ou justification"/>
                        </group>

                        <notebook>
                            <page string="Lignes d'Ajustement" name="lines">
                                <field name="line_ids">
                                    <tree editable="bottom">
                                        <field name="nomenclature_id"/>
                                        <field name="movement_type"/>
                                        <field name="amount" widget="monetary"/>
                                        <field name="description"/>
                                        <field name="currency_id" invisible="1"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <footer>
                        <button name="action_create_adjustment" string="Créer l'Ajustement"
                                type="object" class="oe_highlight"/>
                        <button string="Annuler" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Action pour l'assistant d'ajustement -->
        <record id="action_budget_adjustment_wizard" model="ir.actions.act_window">
            <field name="name">Assistant Ajustement Budgétaire</field>
            <field name="res_model">budget.adjustment.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

        <!-- Vue formulaire de l'assistant d'import -->
        <record id="view_budget_import_wizard_form" model="ir.ui.view">
            <field name="name">budget.import.wizard.form</field>
            <field name="model">budget.import.wizard</field>
            <field name="arch" type="xml">
                <form string="Assistant Import Budgétaire">
                    <sheet>
                        <div class="oe_title">
                            <h1>Importer des Données Budgétaires</h1>
                        </div>

                        <group>
                            <group>
                                <field name="exercise_id"/>
                                <field name="import_type"/>
                            </group>
                            <group>
                                <field name="file_data" filename="file_name"/>
                                <field name="file_name" invisible="1"/>
                            </group>
                        </group>

                        <div class="alert alert-info" role="alert">
                            <strong>Format attendu :</strong>
                            <ul>
                                <li><strong>Crédits budgétaires :</strong> Code poste, Libellé, Montant initial</li>
                                <li><strong>Nomenclature :</strong> Code, Libellé, Code parent, Type, Nature</li>
                            </ul>
                            <p>Formats supportés : CSV (séparateur ;) et Excel (.xlsx)</p>
                        </div>
                    </sheet>
                    <footer>
                        <button name="action_import_data" string="Importer"
                                type="object" class="oe_highlight"/>
                        <button string="Annuler" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Action pour l'assistant d'import -->
        <record id="action_budget_import_wizard" model="ir.actions.act_window">
            <field name="name">Assistant Import Budgétaire</field>
            <field name="res_model">budget.import.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

    </data>
</odoo>
