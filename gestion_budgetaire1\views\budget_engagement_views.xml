<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Vue liste des engagements budgétaires -->
        <record id="view_budget_engagement_tree" model="ir.ui.view">
            <field name="name">budget.engagement.tree</field>
            <field name="model">budget.engagement</field>
            <field name="arch" type="xml">
                <tree string="Engagements Budgétaires" decoration-info="state=='draft'"
                      decoration-warning="state=='waiting_validation'" decoration-success="state=='validated'"
                      decoration-muted="state=='cancelled'">
                    <field name="name"/>
                    <field name="date"/>
                    <field name="exercise_id"/>
                    <field name="nomenclature_id"/>
                    <field name="engagement_type_id"/>
                    <field name="partner_id"/>
                    <field name="amount" widget="monetary"/>
                    <field name="amount_mandated" widget="monetary"/>
                    <field name="amount_remaining" widget="monetary"/>
                    <field name="state" widget="badge"
                           decoration-info="state=='draft'"
                           decoration-warning="state=='waiting_validation'"
                           decoration-success="state=='validated'"
                           decoration-primary="state in ('partially_mandated', 'fully_mandated')"
                           decoration-muted="state=='cancelled'"/>
                    <field name="user_id" widget="many2one_avatar_user"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <!-- Vue formulaire des engagements budgétaires -->
        <record id="view_budget_engagement_form" model="ir.ui.view">
            <field name="name">budget.engagement.form</field>
            <field name="model">budget.engagement</field>
            <field name="arch" type="xml">
                <form string="Engagement Budgétaire">
                    <header>
                        <button name="action_submit_for_validation" string="Soumettre" type="object"
                                class="oe_highlight" invisible="state != 'draft'"
                                groups="gestion_budgetaire.group_budget_agent"/>
                        <button name="action_validate" string="Valider" type="object"
                                class="oe_highlight" invisible="state != 'waiting_validation'"
                                groups="gestion_budgetaire.group_budget_controller"/>
                        <button name="action_create_mandate" string="Créer Mandat" type="object"
                                invisible="state != 'validated' or amount_remaining &lt;= 0"
                                groups="gestion_budgetaire.group_budget_agent"/>
                        <button name="action_cancel" string="Annuler" type="object"
                                invisible="state in ('cancelled', 'fully_mandated')"
                                groups="gestion_budgetaire.group_budget_controller"/>
                        <button name="action_reset_to_draft" string="Remettre en brouillon" type="object"
                                invisible="state not in ('waiting_validation', 'cancelled')"
                                groups="gestion_budgetaire.group_budget_agent"/>
                        <field name="state" widget="statusbar"
                               statusbar_visible="draft,waiting_validation,validated,partially_mandated,fully_mandated"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_mandates" type="object"
                                    class="oe_stat_button" icon="fa-money">
                                <div class="o_stat_info">
                                    <span class="o_stat_text">Mandats</span>
                                </div>
                            </button>
                        </div>

                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>

                        <group>
                            <group>
                                <field name="date" readonly="state in ('validated', 'partially_mandated', 'fully_mandated', 'cancelled')"/>
                                <field name="exercise_id" readonly="state != 'draft'"/>
                                <field name="nomenclature_id" readonly="state != 'draft'"/>
                                <field name="credit_id" readonly="1"/>
                                <field name="engagement_type_id" readonly="state != 'draft'"/>
                            </group>
                            <group>
                                <field name="partner_id" readonly="state in ('validated', 'partially_mandated', 'fully_mandated', 'cancelled')"/>
                                <field name="amount" widget="monetary" readonly="state in ('validated', 'partially_mandated', 'fully_mandated', 'cancelled')"/>
                                <field name="amount_mandated" widget="monetary"/>
                                <field name="amount_remaining" widget="monetary"/>
                                <field name="user_id"/>
                                <field name="currency_id" invisible="1"/>
                                <field name="company_id" invisible="not context.get('show_company', False)"/>
                            </group>
                        </group>

                        <group string="Description">
                            <field name="description" nolabel="1" placeholder="Description de l'engagement"
                                   readonly="state in ('validated', 'partially_mandated', 'fully_mandated', 'cancelled')"/>
                        </group>

                        <group string="Motif" invisible="not reason">
                            <field name="reason" nolabel="1" placeholder="Motif ou justification"
                                   readonly="state in ('validated', 'partially_mandated', 'fully_mandated', 'cancelled')"/>
                        </group>

                        <group string="Documents Sources" invisible="not purchase_order_id and not expense_id and not invoice_id">
                            <group>
                                <field name="purchase_order_id" readonly="1"/>
                                <field name="expense_id" readonly="1"/>
                            </group>
                            <group>
                                <field name="invoice_id" readonly="1"/>
                            </group>
                        </group>

                        <group string="Suivi" invisible="state == 'draft'">
                            <group>
                                <field name="validated_by" readonly="1"/>
                                <field name="validated_date" readonly="1"/>
                            </group>
                        </group>

                        <notebook>
                            <page string="Mandats" name="mandates">
                                <field name="mandate_ids" readonly="state == 'cancelled'">
                                    <tree>
                                        <field name="name"/>
                                        <field name="date"/>
                                        <field name="partner_id"/>
                                        <field name="payment_type_id"/>
                                        <field name="amount" widget="monetary"/>
                                        <field name="state" widget="badge"/>
                                        <field name="currency_id" invisible="1"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Vue kanban des engagements budgétaires -->
        <record id="view_budget_engagement_kanban" model="ir.ui.view">
            <field name="name">budget.engagement.kanban</field>
            <field name="model">budget.engagement</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" default_group_by="state">
                    <field name="name"/>
                    <field name="date"/>
                    <field name="nomenclature_id"/>
                    <field name="partner_id"/>
                    <field name="amount"/>
                    <field name="amount_remaining"/>
                    <field name="state"/>
                    <field name="currency_id"/>
                    <field name="user_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="date"/> - <field name="partner_id"/>
                                        </small>
                                    </div>
                                    <span class="o_kanban_record_top_right">
                                        <field name="user_id" widget="many2one_avatar_user"/>
                                    </span>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="row">
                                        <div class="col-12">
                                            <strong><field name="nomenclature_id"/></strong>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-6">
                                            <span>Montant:</span><br/>
                                            <strong><field name="amount" widget="monetary"/></strong>
                                        </div>
                                        <div class="col-6">
                                            <span>Reste:</span><br/>
                                            <strong><field name="amount_remaining" widget="monetary"/></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Vue recherche des engagements budgétaires -->
        <record id="view_budget_engagement_search" model="ir.ui.view">
            <field name="name">budget.engagement.search</field>
            <field name="model">budget.engagement</field>
            <field name="arch" type="xml">
                <search string="Rechercher Engagements">
                    <field name="name"/>
                    <field name="exercise_id"/>
                    <field name="nomenclature_id"/>
                    <field name="partner_id"/>
                    <field name="engagement_type_id"/>
                    <field name="user_id"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <separator/>
                    <filter string="Mes engagements" name="my_engagements" domain="[('user_id', '=', uid)]"/>
                    <separator/>
                    <filter string="Brouillon" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="En attente" name="waiting" domain="[('state', '=', 'waiting_validation')]"/>
                    <filter string="Validé" name="validated" domain="[('state', '=', 'validated')]"/>
                    <filter string="Partiellement mandaté" name="partially_mandated" domain="[('state', '=', 'partially_mandated')]"/>
                    <filter string="Totalement mandaté" name="fully_mandated" domain="[('state', '=', 'fully_mandated')]"/>
                    <filter string="Annulé" name="cancelled" domain="[('state', '=', 'cancelled')]"/>
                    <separator/>
                    <filter string="À mandater" name="to_mandate" domain="[('state', '=', 'validated'), ('amount_remaining', '>', 0)]"/>
                    <separator/>
                    <filter string="Ce mois" name="this_month"
                            domain="[('date', '&gt;=', context_today().strftime('%Y-%m-01')), ('date', '&lt;', (context_today() + relativedelta(months=1)).strftime('%Y-%m-01'))]"/>
                    <filter string="Exercice actuel" name="current_exercise"
                            domain="[('exercise_id.state', '=', 'open')]"/>
                    <separator/>
                    <group expand="0" string="Grouper par">
                        <filter string="État" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Exercice" name="group_exercise" context="{'group_by': 'exercise_id'}"/>
                        <filter string="Poste budgétaire" name="group_nomenclature" context="{'group_by': 'nomenclature_id'}"/>
                        <filter string="Type d'engagement" name="group_type" context="{'group_by': 'engagement_type_id'}"/>
                        <filter string="Tiers" name="group_partner" context="{'group_by': 'partner_id'}"/>
                        <filter string="Responsable" name="group_user" context="{'group_by': 'user_id'}"/>
                        <filter string="Date" name="group_date" context="{'group_by': 'date:month'}"/>
                        <filter string="Société" name="group_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Action pour les engagements budgétaires -->
        <record id="action_budget_engagement" model="ir.actions.act_window">
            <field name="name">Engagements Budgétaires</field>
            <field name="res_model">budget.engagement</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_engagement_search"/>
            <field name="context">{'search_default_current_exercise': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Créer un nouvel engagement budgétaire
                </p>
                <p>
                    Les engagements budgétaires représentent les dépenses
                    prévues ou réalisées qui impactent le budget.
                </p>
                <p>
                    Ils peuvent être créés manuellement ou automatiquement
                    depuis les commandes d'achat et notes de frais.
                </p>
            </field>
        </record>

        <!-- Action pour les engagements à valider -->
        <record id="action_budget_engagement_to_validate" model="ir.actions.act_window">
            <field name="name">Engagements à Valider</field>
            <field name="res_model">budget.engagement</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_engagement_search"/>
            <field name="domain">[('state', '=', 'waiting_validation')]</field>
            <field name="context">{'search_default_waiting': 1}</field>
        </record>

        <!-- Action pour les engagements à mandater -->
        <record id="action_budget_engagement_to_mandate" model="ir.actions.act_window">
            <field name="name">Engagements à Mandater</field>
            <field name="res_model">budget.engagement</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_engagement_search"/>
            <field name="domain">[('state', '=', 'validated'), ('amount_remaining', '>', 0)]</field>
            <field name="context">{'search_default_to_mandate': 1}</field>
        </record>

    </data>
</odoo>
