# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
from datetime import date, datetime
import logging

_logger = logging.getLogger(__name__)


class BudgetExercise(models.Model):
    """Modèle pour la gestion des exercices budgétaires"""

    _name = 'budget.exercise'
    _description = 'Exercice Budgétaire'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'date_start desc'
    _check_company_auto = True

    # Champs de base
    name = fields.Char(
        string='Nom de l\'exercice',
        required=True,
        help='Nom de l\'exercice budgétaire (ex: Exercice 2024)'
    )

    code = fields.Char(
        string='Code',
        required=True,
        size=10,
        help='Code unique de l\'exercice (ex: EX2024)'
    )

    date_start = fields.Date(
        string='Date de début',
        required=True,
        default=lambda self: date(date.today().year, 1, 1),
        help='Date de début de l\'exercice budgétaire'
    )

    date_end = fields.Date(
        string='Date de fin',
        required=True,
        default=lambda self: date(date.today().year, 12, 31),
        help='Date de fin de l\'exercice budgétaire'
    )

    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('open', 'Ouvert'),
        ('closing', 'En clôture'),
        ('closed', 'Clôturé'),
    ], string='État', default='draft', required=True, tracking=True,
       help='État de l\'exercice budgétaire')

    company_id = fields.Many2one(
        'res.company',
        string='Société',
        required=True,
        default=lambda self: self.env.company,
        help='Société concernée par cet exercice'
    )

    currency_id = fields.Many2one(
        'res.currency',
        string='Devise',
        related='company_id.currency_id',
        store=True,
        readonly=True
    )

    # Champs de description
    description = fields.Text(
        string='Description',
        help='Description détaillée de l\'exercice budgétaire'
    )

    # Champs calculés pour les totaux
    total_credits_initial = fields.Monetary(
        string='Crédits initiaux totaux',
        compute='_compute_totals',
        store=True,
        currency_field='currency_id',
        help='Total des crédits initiaux alloués'
    )

    total_credits_voted = fields.Monetary(
        string='Crédits votés totaux',
        compute='_compute_totals',
        store=True,
        currency_field='currency_id',
        help='Total des crédits après ajustements'
    )

    total_engagements = fields.Monetary(
        string='Total des engagements',
        compute='_compute_totals',
        store=True,
        currency_field='currency_id',
        help='Total des engagements validés'
    )

    total_mandates = fields.Monetary(
        string='Total des mandatements',
        compute='_compute_totals',
        store=True,
        currency_field='currency_id',
        help='Total des mandatements payés'
    )

    total_available_credits = fields.Monetary(
        string='Crédits disponibles',
        compute='_compute_totals',
        store=True,
        currency_field='currency_id',
        help='Crédits disponibles (Crédits votés - Engagements)'
    )

    # Relations
    credit_ids = fields.One2many(
        'budget.credit',
        'exercise_id',
        string='Crédits budgétaires',
        help='Crédits alloués pour cet exercice'
    )

    adjustment_ids = fields.One2many(
        'budget.adjustment',
        'exercise_id',
        string='Ajustements budgétaires',
        help='Ajustements effectués sur cet exercice'
    )

    engagement_ids = fields.One2many(
        'budget.engagement',
        'exercise_id',
        string='Engagements',
        help='Engagements de cet exercice'
    )

    # Contraintes
    @api.constrains('date_start', 'date_end')
    def _check_dates(self):
        """Vérifier la cohérence des dates"""
        for record in self:
            if record.date_start >= record.date_end:
                raise ValidationError(
                    _('La date de fin doit être postérieure à la date de début.')
                )

    @api.constrains('code', 'company_id')
    def _check_unique_code(self):
        """Vérifier l'unicité du code par société"""
        for record in self:
            existing = self.search([
                ('code', '=', record.code),
                ('company_id', '=', record.company_id.id),
                ('id', '!=', record.id)
            ])
            if existing:
                raise ValidationError(
                    _('Le code "%s" existe déjà pour cette société.') % record.code
                )

    @api.constrains('date_start', 'date_end', 'company_id')
    def _check_overlapping_exercises(self):
        """Vérifier qu'il n'y a pas de chevauchement d'exercices ouverts"""
        for record in self:
            if record.state in ('open', 'closing'):
                overlapping = self.search([
                    ('company_id', '=', record.company_id.id),
                    ('state', 'in', ('open', 'closing')),
                    ('id', '!=', record.id),
                    '|',
                    '&', ('date_start', '<=', record.date_start), ('date_end', '>=', record.date_start),
                    '&', ('date_start', '<=', record.date_end), ('date_end', '>=', record.date_end),
                ])
                if overlapping:
                    raise ValidationError(
                        _('Il ne peut y avoir qu\'un seul exercice ouvert à la fois pour une société.')
                    )

    # Méthodes de calcul
    @api.depends('credit_ids.amount_initial', 'credit_ids.amount_voted',
                 'engagement_ids.amount', 'engagement_ids.state')
    def _compute_totals(self):
        """Calculer les totaux de l'exercice"""
        for record in self:
            # Crédits initiaux
            record.total_credits_initial = sum(
                credit.amount_initial for credit in record.credit_ids
            )

            # Crédits votés (après ajustements)
            record.total_credits_voted = sum(
                credit.amount_voted for credit in record.credit_ids
            )

            # Engagements validés
            record.total_engagements = sum(
                engagement.amount for engagement in record.engagement_ids
                if engagement.state == 'validated'
            )

            # Mandatements
            record.total_mandates = sum(
                mandate.amount for mandate in record.engagement_ids.mapped('mandate_ids')
                if mandate.state == 'paid'
            )

            # Crédits disponibles
            record.total_available_credits = record.total_credits_voted - record.total_engagements

    # Actions
    def action_open(self):
        """Ouvrir l'exercice budgétaire"""
        self.ensure_one()
        if self.state != 'draft':
            raise UserError(_('Seul un exercice en brouillon peut être ouvert.'))

        # Vérifier qu'il n'y a pas d'autre exercice ouvert
        existing_open = self.search([
            ('company_id', '=', self.company_id.id),
            ('state', 'in', ('open', 'closing')),
            ('id', '!=', self.id)
        ])
        if existing_open:
            raise UserError(
                _('Il existe déjà un exercice ouvert (%s). Fermez-le avant d\'ouvrir celui-ci.')
                % existing_open[0].name
            )

        self.state = 'open'
        _logger.info('Exercice budgétaire %s ouvert', self.name)

    def action_close(self):
        """Fermer l'exercice budgétaire"""
        self.ensure_one()
        if self.state not in ('open', 'closing'):
            raise UserError(_('Seul un exercice ouvert peut être fermé.'))

        self.state = 'closed'
        _logger.info('Exercice budgétaire %s fermé', self.name)

    def action_set_closing(self):
        """Mettre l'exercice en clôture"""
        self.ensure_one()
        if self.state != 'open':
            raise UserError(_('Seul un exercice ouvert peut être mis en clôture.'))

        self.state = 'closing'
        _logger.info('Exercice budgétaire %s mis en clôture', self.name)

    def action_reopen(self):
        """Rouvrir un exercice"""
        self.ensure_one()
        if self.state != 'closed':
            raise UserError(_('Seul un exercice fermé peut être rouvert.'))

        self.state = 'open'
        _logger.info('Exercice budgétaire %s rouvert', self.name)

    @api.model
    def get_current_exercise(self, company_id=None):
        """Obtenir l'exercice budgétaire actuel"""
        if not company_id:
            company_id = self.env.company.id

        current_exercise = self.search([
            ('company_id', '=', company_id),
            ('state', '=', 'open'),
            ('date_start', '<=', fields.Date.today()),
            ('date_end', '>=', fields.Date.today()),
        ], limit=1)

        return current_exercise

    def name_get(self):
        """Personnaliser l'affichage du nom"""
        result = []
        for record in self:
            name = f"{record.name} ({record.code})"
            result.append((record.id, name))
        return result
