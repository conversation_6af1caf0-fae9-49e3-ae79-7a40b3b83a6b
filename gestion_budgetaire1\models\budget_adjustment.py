# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime
import logging

_logger = logging.getLogger(__name__)


class BudgetAdjustment(models.Model):
    """Modèle pour la gestion des ajustements budgétaires"""
    
    _name = 'budget.adjustment'
    _description = 'Ajustement Budgétaire'
    _order = 'date desc, name desc'
    _check_company_auto = True
    _inherit = ['mail.thread', 'mail.activity.mixin']
    
    # Champs de base
    name = fields.Char(
        string='Référence',
        required=True,
        copy=False,
        readonly=True,
        default=lambda self: _('Nouveau'),
        help='Référence unique de l\'ajustement'
    )
    
    date = fields.Date(
        string='Date d\'ajustement',
        required=True,
        default=fields.Date.context_today,
        tracking=True,
        help='Date de l\'ajustement budgétaire'
    )
    
    exercise_id = fields.Many2one(
        'budget.exercise',
        string='Exercice budgétaire',
        required=True,
        domain="[('state', 'in', ['open', 'closing']), ('company_id', '=', company_id)]",
        help='Exercice budgétaire concerné'
    )
    
    # Type d'ajustement
    adjustment_type = fields.Selection([
        ('transfer', 'Virement de crédits'),
        ('supplementary', 'Budget supplémentaire'),
        ('rectification', 'Budget rectificatif'),
        ('carryover', 'Rattachement de crédits'),
        ('cancellation', 'Annulation de crédits'),
    ], string='Type d\'ajustement', required=True, tracking=True,
       help='Type d\'ajustement budgétaire')
    
    # Société
    company_id = fields.Many2one(
        'res.company',
        string='Société',
        required=True,
        default=lambda self: self.env.company,
        help='Société concernée'
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Devise',
        related='company_id.currency_id',
        store=True,
        readonly=True
    )
    
    # État et workflow
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('waiting_validation', 'En attente de validation'),
        ('validated', 'Validé'),
        ('cancelled', 'Annulé'),
    ], string='État', default='draft', required=True, tracking=True,
       help='État de l\'ajustement')
    
    # Description et motif
    description = fields.Text(
        string='Description',
        required=True,
        help='Description de l\'ajustement'
    )
    
    reason = fields.Text(
        string='Motif',
        required=True,
        help='Motif ou justification de l\'ajustement'
    )
    
    # Lignes d'ajustement
    line_ids = fields.One2many(
        'budget.adjustment.line',
        'adjustment_id',
        string='Lignes d\'ajustement',
        help='Détail des ajustements par poste budgétaire'
    )
    
    # Montants calculés
    total_debit = fields.Monetary(
        string='Total débit',
        compute='_compute_totals',
        store=True,
        currency_field='currency_id',
        help='Total des montants en débit'
    )
    
    total_credit = fields.Monetary(
        string='Total crédit',
        compute='_compute_totals',
        store=True,
        currency_field='currency_id',
        help='Total des montants en crédit'
    )
    
    balance = fields.Monetary(
        string='Solde',
        compute='_compute_totals',
        store=True,
        currency_field='currency_id',
        help='Différence entre débit et crédit'
    )
    
    # Champs de suivi
    user_id = fields.Many2one(
        'res.users',
        string='Responsable',
        default=lambda self: self.env.user,
        help='Utilisateur responsable de l\'ajustement'
    )
    
    validated_by = fields.Many2one(
        'res.users',
        string='Validé par',
        readonly=True,
        help='Utilisateur ayant validé l\'ajustement'
    )
    
    validated_date = fields.Datetime(
        string='Date de validation',
        readonly=True,
        help='Date et heure de validation'
    )
    
    # Contraintes
    @api.constrains('date', 'exercise_id')
    def _check_date_in_exercise(self):
        """Vérifier que la date est dans l'exercice"""
        for record in self:
            if not (record.exercise_id.date_start <= record.date <= record.exercise_id.date_end):
                raise ValidationError(
                    _('La date d\'ajustement doit être comprise dans l\'exercice budgétaire.')
                )
    
    @api.constrains('line_ids')
    def _check_lines_exist(self):
        """Vérifier qu'il y a au moins une ligne"""
        for record in self:
            if not record.line_ids:
                raise ValidationError(
                    _('Un ajustement doit contenir au moins une ligne.')
                )
    
    @api.constrains('balance', 'adjustment_type')
    def _check_balance(self):
        """Vérifier l'équilibre selon le type d'ajustement"""
        for record in self:
            if record.adjustment_type == 'transfer' and abs(record.balance) > 0.01:
                raise ValidationError(
                    _('Un virement de crédits doit être équilibré (débit = crédit).')
                )
    
    # Méthodes de calcul
    @api.depends('line_ids.amount', 'line_ids.movement_type')
    def _compute_totals(self):
        """Calculer les totaux"""
        for record in self:
            debit_lines = record.line_ids.filtered(lambda l: l.movement_type == 'debit')
            credit_lines = record.line_ids.filtered(lambda l: l.movement_type == 'credit')
            
            record.total_debit = sum(debit_lines.mapped('amount'))
            record.total_credit = sum(credit_lines.mapped('amount'))
            record.balance = record.total_debit - record.total_credit
    
    # Méthodes CRUD
    @api.model_create_multi
    def create(self, vals_list):
        """Créer un ajustement avec numérotation automatique"""
        for vals in vals_list:
            if vals.get('name', _('Nouveau')) == _('Nouveau'):
                sequence_code = f'budget.adjustment.{vals.get("adjustment_type", "transfer")}'
                vals['name'] = self.env['ir.sequence'].next_by_code(sequence_code) or _('Nouveau')
        
        return super().create(vals_list)
    
    # Actions de workflow
    def action_submit_for_validation(self):
        """Soumettre pour validation"""
        for record in self:
            if record.state != 'draft':
                raise UserError(_('Seul un ajustement en brouillon peut être soumis.'))
            
            # Vérifications avant soumission
            record._check_lines_validity()
            
            record.state = 'waiting_validation'
    
    def action_validate(self):
        """Valider l'ajustement"""
        for record in self:
            if record.state != 'waiting_validation':
                raise UserError(_('Seul un ajustement en attente peut être validé.'))
            
            # Vérifications avant validation
            record._check_lines_validity()
            
            # Appliquer les ajustements aux crédits
            record._apply_adjustments()
            
            record.write({
                'state': 'validated',
                'validated_by': self.env.user.id,
                'validated_date': datetime.now(),
            })
            
            _logger.info('Ajustement budgétaire %s validé par %s', record.name, self.env.user.name)
    
    def action_cancel(self):
        """Annuler l'ajustement"""
        for record in self:
            if record.state == 'validated':
                raise UserError(_('Un ajustement validé ne peut pas être annulé directement. '
                                'Créez un ajustement inverse.'))
            
            record.state = 'cancelled'
            _logger.info('Ajustement budgétaire %s annulé', record.name)
    
    def action_reset_to_draft(self):
        """Remettre en brouillon"""
        for record in self:
            if record.state not in ('waiting_validation', 'cancelled'):
                raise UserError(_('Seul un ajustement en attente ou annulé peut être remis en brouillon.'))
            record.state = 'draft'
    
    # Méthodes de validation
    def _check_lines_validity(self):
        """Vérifier la validité des lignes"""
        self.ensure_one()
        
        for line in self.line_ids:
            # Vérifier que le crédit existe
            if not line.credit_id:
                raise UserError(
                    _('Aucun crédit budgétaire trouvé pour le poste %s sur l\'exercice %s') %
                    (line.nomenclature_id.name, self.exercise_id.name)
                )
            
            # Vérifier la disponibilité pour les débits
            if line.movement_type == 'debit':
                available = line.credit_id.amount_available
                if line.amount > available:
                    raise UserError(
                        _('Crédit insuffisant sur %s. Disponible: %s, Demandé: %s') %
                        (line.nomenclature_id.name, available, line.amount)
                    )
    
    def _apply_adjustments(self):
        """Appliquer les ajustements aux crédits budgétaires"""
        self.ensure_one()
        
        for line in self.line_ids:
            if line.credit_id:
                # Créer une ligne d'ajustement liée au crédit
                line.credit_id = line.credit_id.id
    
    # Actions de vue
    def action_view_credits(self):
        """Voir les crédits impactés"""
        self.ensure_one()
        credit_ids = self.line_ids.mapped('credit_id').ids
        
        return {
            'name': _('Crédits impactés'),
            'type': 'ir.actions.act_window',
            'res_model': 'budget.credit',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', credit_ids)],
        }


class BudgetAdjustmentLine(models.Model):
    """Lignes d'ajustement budgétaire"""
    
    _name = 'budget.adjustment.line'
    _description = 'Ligne d\'Ajustement Budgétaire'
    _order = 'sequence, id'
    
    # Champs de base
    sequence = fields.Integer(
        string='Séquence',
        default=10,
        help='Ordre d\'affichage'
    )
    
    adjustment_id = fields.Many2one(
        'budget.adjustment',
        string='Ajustement',
        required=True,
        ondelete='cascade',
        help='Ajustement budgétaire parent'
    )
    
    nomenclature_id = fields.Many2one(
        'budget.nomenclature',
        string='Poste budgétaire',
        required=True,
        domain="[('is_analytical', '=', True), ('company_id', '=', company_id)]",
        help='Poste budgétaire concerné'
    )
    
    credit_id = fields.Many2one(
        'budget.credit',
        string='Crédit budgétaire',
        compute='_compute_credit_id',
        store=True,
        help='Crédit budgétaire correspondant'
    )
    
    # Type de mouvement
    movement_type = fields.Selection([
        ('debit', 'Débit (Diminution)'),
        ('credit', 'Crédit (Augmentation)'),
    ], string='Type de mouvement', required=True,
       help='Type de mouvement budgétaire')
    
    # Montant
    amount = fields.Monetary(
        string='Montant',
        currency_field='currency_id',
        required=True,
        help='Montant de l\'ajustement'
    )
    
    # Champs liés
    exercise_id = fields.Many2one(
        'budget.exercise',
        string='Exercice',
        related='adjustment_id.exercise_id',
        store=True,
        readonly=True
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Société',
        related='adjustment_id.company_id',
        store=True,
        readonly=True
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Devise',
        related='company_id.currency_id',
        store=True,
        readonly=True
    )
    
    # Description
    description = fields.Text(
        string='Description',
        help='Description de cette ligne d\'ajustement'
    )
    
    # Contraintes
    @api.constrains('amount')
    def _check_amount_positive(self):
        """Vérifier que le montant est positif"""
        for record in self:
            if record.amount <= 0:
                raise ValidationError(
                    _('Le montant doit être positif.')
                )
    
    # Méthodes de calcul
    @api.depends('exercise_id', 'nomenclature_id')
    def _compute_credit_id(self):
        """Calculer le crédit budgétaire correspondant"""
        for record in self:
            if record.exercise_id and record.nomenclature_id:
                credit = self.env['budget.credit'].search([
                    ('exercise_id', '=', record.exercise_id.id),
                    ('nomenclature_id', '=', record.nomenclature_id.id)
                ], limit=1)
                record.credit_id = credit
            else:
                record.credit_id = False
    
    # Méthodes utilitaires
    def get_signed_amount(self):
        """Obtenir le montant signé selon le type de mouvement"""
        self.ensure_one()
        return self.amount if self.movement_type == 'credit' else -self.amount
    
    @api.onchange('nomenclature_id')
    def _onchange_nomenclature_id(self):
        """Vérifier l'existence du crédit lors du changement de poste"""
        if self.nomenclature_id and self.exercise_id:
            credit = self.env['budget.credit'].search([
                ('exercise_id', '=', self.exercise_id.id),
                ('nomenclature_id', '=', self.nomenclature_id.id)
            ], limit=1)
            
            if not credit:
                return {
                    'warning': {
                        'title': _('Attention'),
                        'message': _('Aucun crédit budgétaire trouvé pour ce poste sur cet exercice.')
                    }
                }
    
    def name_get(self):
        """Personnaliser l'affichage du nom"""
        result = []
        for record in self:
            name = f"{record.nomenclature_id.complete_code} - {record.movement_type} - {record.amount}"
            result.append((record.id, name))
        return result
