<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Vue liste des types d'engagement -->
        <record id="view_budget_engagement_type_tree" model="ir.ui.view">
            <field name="name">budget.engagement.type.tree</field>
            <field name="model">budget.engagement.type</field>
            <field name="arch" type="xml">
                <tree string="Types d'Engagement">
                    <field name="sequence" widget="handle"/>
                    <field name="code"/>
                    <field name="name"/>
                    <field name="budget_impact" widget="badge" 
                           decoration-info="budget_impact=='consumption'"
                           decoration-success="budget_impact=='liberation'"
                           decoration-warning="budget_impact=='neutral'"/>
                    <field name="requires_validation" widget="boolean_toggle"/>
                    <field name="auto_create_mandate" widget="boolean_toggle"/>
                    <field name="allow_negative_amount" widget="boolean_toggle"/>
                    <field name="active" widget="boolean_toggle"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>
        
        <!-- Vue formulaire des types d'engagement -->
        <record id="view_budget_engagement_type_form" model="ir.ui.view">
            <field name="name">budget.engagement.type.form</field>
            <field name="model">budget.engagement.type</field>
            <field name="arch" type="xml">
                <form string="Type d'Engagement">
                    <sheet>
                        <widget name="web_ribbon" title="Archivé" bg_color="bg-danger" 
                                invisible="active"/>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="code" placeholder="Code"/>
                                <span> - </span>
                                <field name="name" placeholder="Nom du type d'engagement"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="sequence"/>
                                <field name="budget_impact"/>
                                <field name="impact_sign" readonly="1"/>
                                <field name="active"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                            <group>
                                <field name="requires_validation"/>
                                <field name="auto_create_mandate"/>
                                <field name="allow_negative_amount"/>
                                <field name="color" widget="color"/>
                            </group>
                        </group>
                        
                        <field name="description" placeholder="Description détaillée du type d'engagement"/>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Action pour les types d'engagement -->
        <record id="action_budget_engagement_type" model="ir.actions.act_window">
            <field name="name">Types d'Engagement</field>
            <field name="res_model">budget.engagement.type</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Créer un nouveau type d'engagement
                </p>
                <p>
                    Les types d'engagement définissent les différentes 
                    catégories d'engagements budgétaires et leur impact.
                </p>
            </field>
        </record>
        
        <!-- Vue liste des types de paiement -->
        <record id="view_budget_payment_type_tree" model="ir.ui.view">
            <field name="name">budget.payment.type.tree</field>
            <field name="model">budget.payment.type</field>
            <field name="arch" type="xml">
                <tree string="Types de Paiement">
                    <field name="sequence" widget="handle"/>
                    <field name="code"/>
                    <field name="name"/>
                    <field name="requires_bank_account" widget="boolean_toggle"/>
                    <field name="is_electronic" widget="boolean_toggle"/>
                    <field name="max_amount" widget="monetary"/>
                    <field name="active" widget="boolean_toggle"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>
        
        <!-- Vue formulaire des types de paiement -->
        <record id="view_budget_payment_type_form" model="ir.ui.view">
            <field name="name">budget.payment.type.form</field>
            <field name="model">budget.payment.type</field>
            <field name="arch" type="xml">
                <form string="Type de Paiement">
                    <sheet>
                        <widget name="web_ribbon" title="Archivé" bg_color="bg-danger" 
                                invisible="active"/>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="code" placeholder="Code"/>
                                <span> - </span>
                                <field name="name" placeholder="Nom du type de paiement"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="sequence"/>
                                <field name="requires_bank_account"/>
                                <field name="is_electronic"/>
                                <field name="active"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                                <field name="currency_id" invisible="1"/>
                            </group>
                            <group>
                                <field name="max_amount" widget="monetary"/>
                            </group>
                        </group>
                        
                        <field name="description" placeholder="Description détaillée du type de paiement"/>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Action pour les types de paiement -->
        <record id="action_budget_payment_type" model="ir.actions.act_window">
            <field name="name">Types de Paiement</field>
            <field name="res_model">budget.payment.type</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Créer un nouveau type de paiement
                </p>
                <p>
                    Les types de paiement définissent les différentes 
                    méthodes de paiement disponibles pour les mandats.
                </p>
            </field>
        </record>
        
        <!-- Configuration des paramètres -->
        <record id="res_config_settings_view_form_budget" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.budget</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="60"/>
            <field name="inherit_id" ref="base.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//form" position="inside">
                    <app data-string="Gestion Budgétaire" string="Gestion Budgétaire" name="gestion_budgetaire" groups="gestion_budgetaire.group_budget_manager">
                        <block title="Contrôle Budgétaire" name="budget_control">
                            <setting string="Contrôle budgétaire sur les lignes d'achat" 
                                     help="Vérifier la disponibilité budgétaire lors de la saisie des lignes de commande d'achat">
                                <field name="budget_check_on_purchase_line"/>
                            </setting>
                            <setting string="Validation automatique des engagements d'achat" 
                                     help="Valider automatiquement les engagements créés depuis les commandes d'achat">
                                <field name="budget_auto_validate_purchase_engagement"/>
                            </setting>
                            <setting string="Création automatique des mandats" 
                                     help="Créer automatiquement les mandats lors de la validation des factures fournisseurs">
                                <field name="budget_auto_create_mandate"/>
                            </setting>
                        </block>
                        <block title="Intégration" name="budget_integration">
                            <setting string="Intégration avec la comptabilité analytique" 
                                     help="Synchroniser les postes budgétaires avec les comptes analytiques">
                                <field name="budget_analytic_integration"/>
                            </setting>
                            <setting string="Génération automatique des titres de perception" 
                                     help="Créer automatiquement les titres de perception depuis les factures clients">
                                <field name="budget_auto_create_perception_title"/>
                            </setting>
                        </block>
                        <block title="Seuils et Alertes" name="budget_thresholds">
                            <setting string="Seuil d'alerte par défaut (%)" 
                                     help="Seuil de consommation budgétaire déclenchant une alerte">
                                <field name="budget_default_alert_threshold"/>
                            </setting>
                            <setting string="Seuil de blocage par défaut (%)" 
                                     help="Seuil de consommation budgétaire déclenchant un blocage automatique">
                                <field name="budget_default_blocking_threshold"/>
                            </setting>
                        </block>
                    </app>
                </xpath>
            </field>
        </record>
        
    </data>
</odoo>
