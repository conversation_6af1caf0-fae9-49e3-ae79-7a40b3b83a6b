<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        
        <!-- Catégorie de module -->
        <record id="module_category_budget" model="ir.module.category">
            <field name="name">Gestion Budgétaire</field>
            <field name="description">Gestion budgétaire pour établissements publics</field>
            <field name="sequence">15</field>
        </record>
        
        <!-- Groupes de sécurité -->
        
        <!-- Utilisateur de base -->
        <record id="group_budget_user" model="res.groups">
            <field name="name">Utilisateur Budget</field>
            <field name="category_id" ref="module_category_budget"/>
            <field name="comment">Peut consulter les données budgétaires et créer des engagements</field>
        </record>
        
        <!-- Agent de saisie -->
        <record id="group_budget_agent" model="res.groups">
            <field name="name">Agent de <PERSON></field>
            <field name="category_id" ref="module_category_budget"/>
            <field name="implied_ids" eval="[(4, ref('group_budget_user'))]"/>
            <field name="comment">Peut saisir et modifier les données budgétaires</field>
        </record>
        
        <!-- Contrôleur budgétaire -->
        <record id="group_budget_controller" model="res.groups">
            <field name="name">Contrôleur Budgétaire</field>
            <field name="category_id" ref="module_category_budget"/>
            <field name="implied_ids" eval="[(4, ref('group_budget_agent'))]"/>
            <field name="comment">Peut valider les engagements et ajustements budgétaires</field>
        </record>
        
        <!-- Gestionnaire budgétaire -->
        <record id="group_budget_manager" model="res.groups">
            <field name="name">Gestionnaire Budget</field>
            <field name="category_id" ref="module_category_budget"/>
            <field name="implied_ids" eval="[(4, ref('group_budget_controller'))]"/>
            <field name="comment">Peut gérer les exercices et la configuration budgétaire</field>
        </record>
        
        <!-- Administrateur budgétaire -->
        <record id="group_budget_admin" model="res.groups">
            <field name="name">Administrateur Budget</field>
            <field name="category_id" ref="module_category_budget"/>
            <field name="implied_ids" eval="[(4, ref('group_budget_manager'))]"/>
            <field name="comment">Accès complet à la gestion budgétaire</field>
        </record>
        
        <!-- Règles de sécurité par enregistrement -->
        
        <!-- Exercices budgétaires -->
        <record id="budget_exercise_rule_user" model="ir.rule">
            <field name="name">Exercices Budget: Utilisateur</field>
            <field name="model_id" ref="model_budget_exercise"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_budget_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="budget_exercise_rule_manager" model="ir.rule">
            <field name="name">Exercices Budget: Gestionnaire</field>
            <field name="model_id" ref="model_budget_exercise"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_budget_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <!-- Nomenclature budgétaire -->
        <record id="budget_nomenclature_rule_user" model="ir.rule">
            <field name="name">Nomenclature Budget: Utilisateur</field>
            <field name="model_id" ref="model_budget_nomenclature"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_budget_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="budget_nomenclature_rule_manager" model="ir.rule">
            <field name="name">Nomenclature Budget: Gestionnaire</field>
            <field name="model_id" ref="model_budget_nomenclature"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_budget_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <!-- Crédits budgétaires -->
        <record id="budget_credit_rule_user" model="ir.rule">
            <field name="name">Crédits Budget: Utilisateur</field>
            <field name="model_id" ref="model_budget_credit"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_budget_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="budget_credit_rule_agent" model="ir.rule">
            <field name="name">Crédits Budget: Agent</field>
            <field name="model_id" ref="model_budget_credit"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_budget_agent'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Engagements budgétaires -->
        <record id="budget_engagement_rule_user" model="ir.rule">
            <field name="name">Engagements Budget: Utilisateur</field>
            <field name="model_id" ref="model_budget_engagement"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_budget_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="budget_engagement_rule_agent" model="ir.rule">
            <field name="name">Engagements Budget: Agent</field>
            <field name="model_id" ref="model_budget_engagement"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_budget_agent'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Mandats -->
        <record id="budget_mandate_rule_user" model="ir.rule">
            <field name="name">Mandats Budget: Utilisateur</field>
            <field name="model_id" ref="model_budget_mandate"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_budget_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="budget_mandate_rule_agent" model="ir.rule">
            <field name="name">Mandats Budget: Agent</field>
            <field name="model_id" ref="model_budget_mandate"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_budget_agent'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Ajustements budgétaires -->
        <record id="budget_adjustment_rule_controller" model="ir.rule">
            <field name="name">Ajustements Budget: Contrôleur</field>
            <field name="model_id" ref="model_budget_adjustment"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_budget_controller'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Titres de perception -->
        <record id="budget_perception_title_rule_user" model="ir.rule">
            <field name="name">Titres Perception: Utilisateur</field>
            <field name="model_id" ref="model_budget_perception_title"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_budget_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="budget_perception_title_rule_agent" model="ir.rule">
            <field name="name">Titres Perception: Agent</field>
            <field name="model_id" ref="model_budget_perception_title"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_budget_agent'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Types d'engagement et de paiement -->
        <record id="budget_engagement_type_rule_manager" model="ir.rule">
            <field name="name">Types Engagement: Gestionnaire</field>
            <field name="model_id" ref="model_budget_engagement_type"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_budget_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="budget_payment_type_rule_manager" model="ir.rule">
            <field name="name">Types Paiement: Gestionnaire</field>
            <field name="model_id" ref="model_budget_payment_type"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('group_budget_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
    </data>
</odoo>
