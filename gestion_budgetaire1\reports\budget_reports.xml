<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Rapport de situation budgétaire -->
        <record id="view_budget_situation_report_tree" model="ir.ui.view">
            <field name="name">budget.situation.report.tree</field>
            <field name="model">budget.situation.report</field>
            <field name="arch" type="xml">
                <tree string="Situation Budgétaire" create="false" edit="false" delete="false">
                    <field name="exercise_id"/>
                    <field name="nomenclature_code"/>
                    <field name="nomenclature_name"/>
                    <field name="amount_initial" widget="monetary"/>
                    <field name="amount_adjustments" widget="monetary"/>
                    <field name="amount_voted" widget="monetary"/>
                    <field name="amount_engaged" widget="monetary"/>
                    <field name="amount_available" widget="monetary" 
                           decoration-danger="amount_available &lt; 0"/>
                    <field name="consumption_rate" widget="percentage" 
                           decoration-warning="consumption_rate &gt;= 80 and consumption_rate &lt; 100"
                           decoration-danger="consumption_rate &gt;= 100"/>
                    <field name="amount_mandated" widget="monetary"/>
                    <field name="amount_to_pay" widget="monetary"/>
                    <field name="payment_rate" widget="percentage"/>
                    <field name="currency_id" invisible="1"/>
                </tree>
            </field>
        </record>
        
        <record id="view_budget_situation_report_search" model="ir.ui.view">
            <field name="name">budget.situation.report.search</field>
            <field name="model">budget.situation.report</field>
            <field name="arch" type="xml">
                <search string="Rechercher">
                    <field name="exercise_id"/>
                    <field name="nomenclature_code"/>
                    <field name="nomenclature_name"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <separator/>
                    <filter string="Crédit disponible" name="available" domain="[('amount_available', '>', 0)]"/>
                    <filter string="Crédit épuisé" name="exhausted" domain="[('amount_available', '&lt;=', 0)]"/>
                    <filter string="Seuil d'alerte" name="alert" domain="[('consumption_rate', '&gt;=', 80)]"/>
                    <filter string="Dépassement" name="exceeded" domain="[('consumption_rate', '&gt;=', 100)]"/>
                    <separator/>
                    <group expand="0" string="Grouper par">
                        <filter string="Exercice" name="group_exercise" context="{'group_by': 'exercise_id'}"/>
                        <filter string="Poste budgétaire" name="group_nomenclature" context="{'group_by': 'nomenclature_id'}"/>
                        <filter string="Société" name="group_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>
        
        <record id="action_budget_situation_report" model="ir.actions.act_window">
            <field name="name">Situation Budgétaire</field>
            <field name="res_model">budget.situation.report</field>
            <field name="view_mode">tree</field>
            <field name="search_view_id" ref="view_budget_situation_report_search"/>
        </record>
        
        <!-- Rapport des engagements -->
        <record id="view_budget_engagement_report_tree" model="ir.ui.view">
            <field name="name">budget.engagement.report.tree</field>
            <field name="model">budget.engagement.report</field>
            <field name="arch" type="xml">
                <tree string="Rapport Engagements" create="false" edit="false" delete="false">
                    <field name="exercise_id"/>
                    <field name="date"/>
                    <field name="nomenclature_id"/>
                    <field name="engagement_type_id"/>
                    <field name="partner_id"/>
                    <field name="engagement_count"/>
                    <field name="amount" widget="monetary"/>
                    <field name="amount_mandated" widget="monetary"/>
                    <field name="amount_remaining" widget="monetary"/>
                    <field name="state" widget="badge"/>
                    <field name="currency_id" invisible="1"/>
                </tree>
            </field>
        </record>
        
        <record id="view_budget_engagement_report_search" model="ir.ui.view">
            <field name="name">budget.engagement.report.search</field>
            <field name="model">budget.engagement.report</field>
            <field name="arch" type="xml">
                <search string="Rechercher">
                    <field name="exercise_id"/>
                    <field name="nomenclature_id"/>
                    <field name="engagement_type_id"/>
                    <field name="partner_id"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <separator/>
                    <filter string="Validés" name="validated" domain="[('state', '=', 'validated')]"/>
                    <filter string="À mandater" name="to_mandate" domain="[('state', '=', 'validated'), ('amount_remaining', '>', 0)]"/>
                    <separator/>
                    <filter string="Ce mois" name="this_month" domain="[('month', '=', context_today().strftime('%Y-%m'))]"/>
                    <filter string="Cette année" name="this_year" domain="[('year', '=', context_today().year)]"/>
                    <separator/>
                    <group expand="0" string="Grouper par">
                        <filter string="Exercice" name="group_exercise" context="{'group_by': 'exercise_id'}"/>
                        <filter string="Poste budgétaire" name="group_nomenclature" context="{'group_by': 'nomenclature_id'}"/>
                        <filter string="Type d'engagement" name="group_type" context="{'group_by': 'engagement_type_id'}"/>
                        <filter string="Tiers" name="group_partner" context="{'group_by': 'partner_id'}"/>
                        <filter string="État" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Mois" name="group_month" context="{'group_by': 'month'}"/>
                        <filter string="Année" name="group_year" context="{'group_by': 'year'}"/>
                        <filter string="Société" name="group_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>
        
        <record id="action_budget_engagement_report" model="ir.actions.act_window">
            <field name="name">Rapport Engagements</field>
            <field name="res_model">budget.engagement.report</field>
            <field name="view_mode">tree</field>
            <field name="search_view_id" ref="view_budget_engagement_report_search"/>
        </record>
        
        <!-- Rapport des mandats -->
        <record id="view_budget_mandate_report_tree" model="ir.ui.view">
            <field name="name">budget.mandate.report.tree</field>
            <field name="model">budget.mandate.report</field>
            <field name="arch" type="xml">
                <tree string="Rapport Mandats" create="false" edit="false" delete="false">
                    <field name="exercise_id"/>
                    <field name="date"/>
                    <field name="payment_date"/>
                    <field name="nomenclature_id"/>
                    <field name="payment_type_id"/>
                    <field name="partner_id"/>
                    <field name="mandate_count"/>
                    <field name="amount" widget="monetary"/>
                    <field name="state" widget="badge"/>
                    <field name="currency_id" invisible="1"/>
                </tree>
            </field>
        </record>
        
        <record id="view_budget_mandate_report_search" model="ir.ui.view">
            <field name="name">budget.mandate.report.search</field>
            <field name="model">budget.mandate.report</field>
            <field name="arch" type="xml">
                <search string="Rechercher">
                    <field name="exercise_id"/>
                    <field name="nomenclature_id"/>
                    <field name="payment_type_id"/>
                    <field name="partner_id"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <separator/>
                    <filter string="Validés" name="validated" domain="[('state', '=', 'validated')]"/>
                    <filter string="Payés" name="paid" domain="[('state', '=', 'paid')]"/>
                    <separator/>
                    <filter string="Ce mois" name="this_month" domain="[('month', '=', context_today().strftime('%Y-%m'))]"/>
                    <filter string="Cette année" name="this_year" domain="[('year', '=', context_today().year)]"/>
                    <separator/>
                    <group expand="0" string="Grouper par">
                        <filter string="Exercice" name="group_exercise" context="{'group_by': 'exercise_id'}"/>
                        <filter string="Poste budgétaire" name="group_nomenclature" context="{'group_by': 'nomenclature_id'}"/>
                        <filter string="Type de paiement" name="group_payment_type" context="{'group_by': 'payment_type_id'}"/>
                        <filter string="Bénéficiaire" name="group_partner" context="{'group_by': 'partner_id'}"/>
                        <filter string="État" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Mois" name="group_month" context="{'group_by': 'month'}"/>
                        <filter string="Année" name="group_year" context="{'group_by': 'year'}"/>
                        <filter string="Société" name="group_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>
        
        <record id="action_budget_mandate_report" model="ir.actions.act_window">
            <field name="name">Rapport Mandats</field>
            <field name="res_model">budget.mandate.report</field>
            <field name="view_mode">tree</field>
            <field name="search_view_id" ref="view_budget_mandate_report_search"/>
        </record>
        
        <!-- Menus des rapports -->
        <menuitem id="menu_budget_report_situation" 
                  name="Situation Budgétaire" 
                  parent="menu_budget_reports" 
                  action="action_budget_situation_report" 
                  sequence="10"
                  groups="gestion_budgetaire.group_budget_user"/>
        
        <menuitem id="menu_budget_report_engagement" 
                  name="Rapport Engagements" 
                  parent="menu_budget_reports" 
                  action="action_budget_engagement_report" 
                  sequence="20"
                  groups="gestion_budgetaire.group_budget_user"/>
        
        <menuitem id="menu_budget_report_mandate" 
                  name="Rapport Mandats" 
                  parent="menu_budget_reports" 
                  action="action_budget_mandate_report" 
                  sequence="30"
                  groups="gestion_budgetaire.group_budget_user"/>
        
    </data>
</odoo>
