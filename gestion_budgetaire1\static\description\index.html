<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Gestion Bud<PERSON>taire - Module Odoo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2E7D32 0%, #1976D2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        .feature {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 8px;
            border-left: 4px solid #2E7D32;
        }
        .feature h3 {
            color: #2E7D32;
            margin-top: 0;
        }
        .feature ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature li {
            margin-bottom: 8px;
        }
        .screenshot {
            text-align: center;
            margin: 40px 0;
        }
        .screenshot img {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .tech-specs {
            background: #e3f2fd;
            padding: 30px;
            border-radius: 8px;
            margin: 40px 0;
        }
        .tech-specs h3 {
            color: #1976D2;
            margin-top: 0;
        }
        .badges {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .badge {
            background: #2E7D32;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .footer {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏛️ Gestion Budgétaire</h1>
            <p>Solution complète de gestion budgétaire pour établissements publics algériens</p>
            <div class="badges">
                <span class="badge">Odoo 17</span>
                <span class="badge">Algérie</span>
                <span class="badge">Secteur Public</span>
                <span class="badge">Multilingue</span>
            </div>
        </div>

        <div class="content">
            <h2>📋 Description</h2>
            <p>
                Ce module offre une solution complète et moderne de gestion budgétaire spécialement conçue 
                pour les établissements publics algériens. Il respecte les normes et réglementations 
                locales tout en offrant une interface intuitive et des fonctionnalités avancées.
            </p>

            <div class="features">
                <div class="feature">
                    <h3>🎯 Gestion des Exercices</h3>
                    <ul>
                        <li>Création et gestion des exercices budgétaires</li>
                        <li>Suivi des états (ouvert, en clôture, fermé)</li>
                        <li>Contrôles de cohérence temporelle</li>
                        <li>Historique complet des exercices</li>
                    </ul>
                </div>

                <div class="feature">
                    <h3>📊 Nomenclature Budgétaire</h3>
                    <ul>
                        <li>Structure hiérarchique configurable</li>
                        <li>Sections, chapitres, articles, paragraphes</li>
                        <li>Séparation dépenses/recettes</li>
                        <li>Intégration comptabilité analytique</li>
                    </ul>
                </div>

                <div class="feature">
                    <h3>💰 Crédits et Contrôle</h3>
                    <ul>
                        <li>Gestion des crédits budgétaires</li>
                        <li>Contrôle en temps réel</li>
                        <li>Seuils d'alerte configurables</li>
                        <li>Blocage automatique des dépassements</li>
                    </ul>
                </div>

                <div class="feature">
                    <h3>📝 Engagements</h3>
                    <ul>
                        <li>Création manuelle ou automatique</li>
                        <li>Workflow de validation</li>
                        <li>Types d'engagement configurables</li>
                        <li>Suivi des mandatements</li>
                    </ul>
                </div>

                <div class="feature">
                    <h3>💳 Mandats de Paiement</h3>
                    <ul>
                        <li>Génération depuis les engagements</li>
                        <li>Types de paiement multiples</li>
                        <li>Validation et suivi des paiements</li>
                        <li>Intégration comptable</li>
                    </ul>
                </div>

                <div class="feature">
                    <h3>🔄 Ajustements</h3>
                    <ul>
                        <li>Virements de crédits</li>
                        <li>Budgets supplémentaires</li>
                        <li>Budgets rectificatifs</li>
                        <li>Rattachements de crédits</li>
                    </ul>
                </div>

                <div class="feature">
                    <h3>📈 Recettes</h3>
                    <ul>
                        <li>Titres de perception</li>
                        <li>Suivi des encaissements</li>
                        <li>Gestion des échéances</li>
                        <li>Rapports de recouvrement</li>
                    </ul>
                </div>

                <div class="feature">
                    <h3>🔗 Intégrations</h3>
                    <ul>
                        <li>Module Achats (commandes)</li>
                        <li>Module Ventes (factures clients)</li>
                        <li>Module Comptabilité</li>
                        <li>Module Notes de frais</li>
                    </ul>
                </div>
            </div>

            <div class="tech-specs">
                <h3>🛠️ Spécifications Techniques</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div>
                        <strong>Version Odoo :</strong> 17.0+<br>
                        <strong>Licence :</strong> LGPL-3<br>
                        <strong>Type :</strong> Application<br>
                        <strong>Catégorie :</strong> Comptabilité
                    </div>
                    <div>
                        <strong>Dépendances :</strong><br>
                        • base, account, purchase<br>
                        • sale, hr_expense, analytic<br>
                        <strong>Multi-société :</strong> ✅<br>
                        <strong>Multi-devise :</strong> ✅
                    </div>
                </div>
            </div>

            <h2>🚀 Installation</h2>
            <ol>
                <li>Télécharger le module dans le répertoire addons d'Odoo</li>
                <li>Redémarrer le serveur Odoo</li>
                <li>Activer le mode développeur</li>
                <li>Aller dans Apps → Mettre à jour la liste des applications</li>
                <li>Rechercher "Gestion Budgétaire" et installer</li>
            </ol>

            <h2>⚙️ Configuration</h2>
            <ol>
                <li>Aller dans Gestion Budgétaire → Configuration</li>
                <li>Configurer les types d'engagement et de paiement</li>
                <li>Créer la nomenclature budgétaire</li>
                <li>Créer un exercice budgétaire</li>
                <li>Définir les crédits budgétaires</li>
            </ol>

            <h2>👥 Groupes de Sécurité</h2>
            <ul>
                <li><strong>Utilisateur Budget :</strong> Consultation des données</li>
                <li><strong>Agent de Saisie :</strong> Création et modification</li>
                <li><strong>Contrôleur Budgétaire :</strong> Validation des opérations</li>
                <li><strong>Gestionnaire Budget :</strong> Configuration et gestion</li>
                <li><strong>Administrateur Budget :</strong> Accès complet</li>
            </ul>

            <h2>📊 Rapports Disponibles</h2>
            <ul>
                <li>Situation budgétaire par poste</li>
                <li>Rapport des engagements</li>
                <li>Rapport des mandats</li>
                <li>Suivi des recettes</li>
                <li>Tableaux de bord interactifs</li>
            </ul>
        </div>

        <div class="footer">
            <p>
                <strong>Gestion Budgétaire v1.0.0</strong><br>
                Module Odoo pour la gestion budgétaire des établissements publics algériens<br>
                Développé avec ❤️ pour la communauté Odoo
            </p>
        </div>
    </div>
</body>
</html>
