# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
from datetime import date


class TestBudgetCredit(TransactionCase):
    """Tests pour le modèle budget.credit"""

    def setUp(self):
        super().setUp()
        self.BudgetCredit = self.env['budget.credit']
        self.BudgetExercise = self.env['budget.exercise']
        self.BudgetNomenclature = self.env['budget.nomenclature']
        
        self.company = self.env.ref('base.main_company')
        
        # Créer un exercice de test
        self.exercise = self.BudgetExercise.create({
            'name': 'Test Exercise 2024',
            'code': 'TEST2024',
            'date_start': date(2024, 1, 1),
            'date_end': date(2024, 12, 31),
            'company_id': self.company.id,
            'state': 'open',
        })
        
        # Créer une nomenclature de test
        self.nomenclature = self.BudgetNomenclature.create({
            'name': 'Test Nomenclature',
            'code': 'TEST001',
            'budget_type': 'expense',
            'nature': 'article',
            'is_analytical': True,
            'company_id': self.company.id,
        })

    def test_create_credit(self):
        """Test de création d'un crédit budgétaire"""
        credit = self.BudgetCredit.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'amount_initial': 100000.0,
        })
        
        self.assertEqual(credit.state, 'draft')
        self.assertEqual(credit.amount_initial, 100000.0)
        self.assertEqual(credit.amount_voted, 100000.0)
        self.assertEqual(credit.amount_available, 100000.0)

    def test_credit_amount_validation(self):
        """Test de validation du montant de crédit"""
        # Test montant négatif
        with self.assertRaises(ValidationError):
            self.BudgetCredit.create({
                'exercise_id': self.exercise.id,
                'nomenclature_id': self.nomenclature.id,
                'amount_initial': -50000.0,
            })

    def test_credit_uniqueness(self):
        """Test d'unicité du crédit par exercice et nomenclature"""
        # Créer le premier crédit
        self.BudgetCredit.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'amount_initial': 100000.0,
        })
        
        # Tenter de créer un second crédit pour la même combinaison
        with self.assertRaises(ValidationError):
            self.BudgetCredit.create({
                'exercise_id': self.exercise.id,
                'nomenclature_id': self.nomenclature.id,
                'amount_initial': 50000.0,
            })

    def test_credit_workflow(self):
        """Test du workflow d'un crédit"""
        credit = self.BudgetCredit.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'amount_initial': 80000.0,
        })
        
        # Test validation
        credit.action_validate()
        self.assertEqual(credit.state, 'validated')
        
        # Test blocage
        credit.action_block()
        self.assertEqual(credit.state, 'blocked')
        self.assertTrue(credit.is_blocked)
        
        # Test déblocage
        credit.action_unblock()
        self.assertEqual(credit.state, 'validated')
        self.assertFalse(credit.is_blocked)

    def test_credit_amounts_computation(self):
        """Test du calcul des montants de crédit"""
        credit = self.BudgetCredit.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'amount_initial': 120000.0,
            'amount_adjustments': 20000.0,
        })
        
        # Vérifier les calculs
        self.assertEqual(credit.amount_voted, 140000.0)  # initial + adjustments
        self.assertEqual(credit.amount_available, 140000.0)  # voted - engaged
        self.assertEqual(credit.consumption_rate, 0.0)  # aucun engagement

    def test_credit_consumption_rate(self):
        """Test du calcul du taux de consommation"""
        credit = self.BudgetCredit.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'amount_initial': 100000.0,
            'state': 'validated',
        })
        
        # Simuler des engagements
        credit.amount_engaged = 75000.0
        credit._compute_amounts()
        
        self.assertEqual(credit.consumption_rate, 75.0)
        self.assertEqual(credit.amount_available, 25000.0)

    def test_credit_payment_rate(self):
        """Test du calcul du taux de paiement"""
        credit = self.BudgetCredit.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'amount_initial': 100000.0,
            'state': 'validated',
        })
        
        # Simuler des engagements et mandatements
        credit.amount_engaged = 80000.0
        credit.amount_mandated = 60000.0
        credit._compute_amounts()
        
        self.assertEqual(credit.payment_rate, 75.0)  # 60000 / 80000 * 100
        self.assertEqual(credit.amount_to_pay, 20000.0)  # 80000 - 60000

    def test_credit_thresholds(self):
        """Test des seuils d'alerte et de blocage"""
        credit = self.BudgetCredit.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'amount_initial': 100000.0,
            'alert_threshold': 80.0,
            'blocking_threshold': 100.0,
            'state': 'validated',
        })
        
        # Test seuil d'alerte
        credit.amount_engaged = 85000.0
        credit._compute_amounts()
        self.assertTrue(credit.consumption_rate >= credit.alert_threshold)
        
        # Test seuil de blocage
        credit.amount_engaged = 100000.0
        credit._compute_amounts()
        self.assertTrue(credit.consumption_rate >= credit.blocking_threshold)

    def test_credit_name_generation(self):
        """Test de génération automatique du nom"""
        credit = self.BudgetCredit.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'amount_initial': 90000.0,
        })
        
        expected_name = f"{self.exercise.code} - {self.nomenclature.complete_code}"
        self.assertEqual(credit.name, expected_name)

    def test_credit_blocking_with_reason(self):
        """Test de blocage avec motif"""
        credit = self.BudgetCredit.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'amount_initial': 70000.0,
            'state': 'validated',
        })
        
        # Bloquer avec un motif
        credit.write({
            'blocking_reason': 'Test de blocage pour audit',
        })
        credit.action_block()
        
        self.assertEqual(credit.state, 'blocked')
        self.assertEqual(credit.blocking_reason, 'Test de blocage pour audit')

    def test_credit_adjustment_impact(self):
        """Test de l'impact des ajustements"""
        credit = self.BudgetCredit.create({
            'exercise_id': self.exercise.id,
            'nomenclature_id': self.nomenclature.id,
            'amount_initial': 100000.0,
        })
        
        # Ajouter un ajustement positif
        credit.amount_adjustments = 25000.0
        credit._compute_amounts()
        
        self.assertEqual(credit.amount_voted, 125000.0)
        self.assertEqual(credit.amount_available, 125000.0)
        
        # Ajouter un ajustement négatif
        credit.amount_adjustments = -15000.0
        credit._compute_amounts()
        
        self.assertEqual(credit.amount_voted, 85000.0)
        self.assertEqual(credit.amount_available, 85000.0)
